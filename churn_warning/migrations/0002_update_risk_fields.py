# Generated by Django 5.2.4 on 2025-08-06 07:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('churn_warning', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='churnprediction',
            name='battle_risk_score',
        ),
        migrations.RemoveField(
            model_name='churnprediction',
            name='social_risk_score',
        ),
        migrations.AddField(
            model_name='churnprediction',
            name='activity_risk_score',
            field=models.FloatField(default=0.0, verbose_name='活跃风险评分'),
        ),
        migrations.AlterField(
            model_name='churnriskfactor',
            name='factor_type',
            field=models.CharField(choices=[('login_frequency', '登录频率'), ('login_duration', '登录时长'), ('recharge_frequency', '充值频率'), ('recharge_amount', '充值金额'), ('activity_risk', '活跃风险'), ('online_time_risk', '在线时长风险'), ('task_completion_risk', '任务完成度风险')], max_length=50, unique=True, verbose_name='风险因子类型'),
        ),
    ]
