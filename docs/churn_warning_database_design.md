# 流失预警中心 - 数据库设计文档

## 📋 目录

1. [数据库概述](#数据库概述)
2. [表结构设计](#表结构设计)
3. [关系图](#关系图)
4. [索引设计](#索引设计)
5. [数据流转](#数据流转)
6. [性能优化](#性能优化)

## 🗄️ 数据库概述

流失预警中心包含5个核心数据表，与大R用户总览模块的用户数据表紧密关联，形成完整的流失预警数据体系。

### 核心表列表

| 表名 | 中文名称 | 主要功能 |
|-----|---------|---------|
| `churn_warning_churnriskfactor` | 风险因子配置表 | 存储风险评估的因子配置和权重 |
| `churn_warning_churnprediction` | 流失预测表 | 存储每个用户的风险评分和预测结果 |
| `churn_warning_churnwarningstats` | 预警统计表 | 存储每日风险统计数据 |
| `churn_warning_churninterventionlog` | 干预记录表 | 记录对风险用户的干预措施和效果 |
| `churn_warning_churnwarningconfig` | 系统配置表 | 存储系统级别的预警配置参数 |

## 🏗️ 表结构设计

### 1. churn_warning_churnriskfactor (风险因子配置表)

**用途**: 配置风险评估算法的各个因子及其权重

```sql
CREATE TABLE churn_warning_churnriskfactor (
    id                  BIGINT PRIMARY KEY AUTO_INCREMENT,
    factor_type         VARCHAR(50) UNIQUE NOT NULL,        -- 风险因子类型
    description         TEXT NOT NULL,                      -- 因子描述
    weight              DECIMAL(3,2) NOT NULL DEFAULT 0.1,  -- 权重(0-1)
    threshold_value     DECIMAL(10,2) NOT NULL DEFAULT 0.0, -- 阈值
    is_active           BOOLEAN NOT NULL DEFAULT TRUE,      -- 是否启用
    created_at          DATETIME NOT NULL,                  -- 创建时间
    updated_at          DATETIME NOT NULL                   -- 更新时间
);
```

**字段说明**:
- `factor_type`: 因子类型，如 `login_frequency`、`recharge_frequency` 等
- `weight`: 该因子在综合评分中的权重，所有活跃因子权重之和建议为1.0
- `threshold_value`: 该因子的风险阈值，超过此值认为有风险
- `is_active`: 是否在计算中使用此因子

**数据示例**:
```sql
INSERT INTO churn_warning_churnriskfactor VALUES
(1, 'login_frequency', '用户登录频率异常', 0.25, 10.0, 1, '2025-08-06 10:00:00', '2025-08-06 10:00:00'),
(2, 'recharge_frequency', '充值频率下降', 0.30, 2.0, 1, '2025-08-06 10:00:00', '2025-08-06 10:00:00'),
(3, 'battle_activity', '战斗活跃度降低', 0.10, 20.0, 1, '2025-08-06 10:00:00', '2025-08-06 10:00:00');
```

### 2. churn_warning_churnprediction (流失预测表)

**用途**: 存储每个用户的风险评估结果和预测信息

```sql
CREATE TABLE churn_warning_churnprediction (
    id                      BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id                 BIGINT UNIQUE NOT NULL,             -- 关联用户ID
    risk_level              VARCHAR(10) NOT NULL,               -- 风险等级
    risk_score              DECIMAL(5,2) NOT NULL,              -- 综合风险评分(0-100)
    
    -- 各维度风险评分
    login_risk_score        DECIMAL(5,2) DEFAULT 0.0,           -- 登录风险评分
    recharge_risk_score     DECIMAL(5,2) DEFAULT 0.0,           -- 充值风险评分
    battle_risk_score       DECIMAL(5,2) DEFAULT 0.0,           -- 战斗风险评分
    social_risk_score       DECIMAL(5,2) DEFAULT 0.0,           -- 社交风险评分
    
    -- 预测相关
    predicted_churn_date    DATE NULL,                          -- 预测流失日期
    confidence_score        DECIMAL(3,2) DEFAULT 0.0,          -- 预测置信度
    
    -- 扩展信息 (JSON字段)
    risk_factors            JSON DEFAULT NULL,                  -- 风险因素标签
    suggested_actions       JSON DEFAULT NULL,                  -- 建议措施
    
    -- 处理状态
    status                  VARCHAR(20) DEFAULT 'pending',     -- 处理状态
    
    -- 时间戳
    created_at              DATETIME NOT NULL,
    updated_at              DATETIME NOT NULL,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES big_r_overview_bigruser(id) ON DELETE CASCADE
);
```

**字段说明**:
- `risk_level`: 风险等级枚举值 (`low`, `medium`, `high`, `critical`)
- `risk_score`: 0-100的综合风险评分
- `*_risk_score`: 各个维度的子评分
- `predicted_churn_date`: 基于风险评分预测的流失日期
- `risk_factors`: JSON格式存储的风险因素标签数组
- `suggested_actions`: JSON格式存储的建议干预措施
- `status`: 处理状态 (`pending`, `contacted`, `intervened`, `recovered`, `churned`)

**JSON字段结构示例**:
```json
// risk_factors 示例
[
    {
        "type": "login",
        "text": "登录风险", 
        "score": 75.5,
        "is_primary": true
    },
    {
        "type": "recharge",
        "text": "充值风险",
        "score": 82.3, 
        "is_primary": true
    }
]

// suggested_actions 示例
[
    {
        "type": "contact",
        "text": "立即联系用户",
        "priority": "high"
    },
    {
        "type": "gift", 
        "text": "赠送高价值礼包",
        "priority": "high"
    }
]
```

### 3. churn_warning_churnwarningstats (预警统计表)

**用途**: 存储每日的风险用户统计数据和趋势分析

```sql
CREATE TABLE churn_warning_churnwarningstats (
    id                          BIGINT PRIMARY KEY AUTO_INCREMENT,
    stat_date                   DATE UNIQUE NOT NULL,           -- 统计日期
    
    -- 各风险等级用户数量
    critical_users              INTEGER DEFAULT 0,              -- 严重风险用户数
    high_risk_users             INTEGER DEFAULT 0,              -- 高风险用户数
    medium_risk_users           INTEGER DEFAULT 0,              -- 中等风险用户数
    low_risk_users              INTEGER DEFAULT 0,              -- 低风险用户数
    
    -- 处理统计
    contacted_users             INTEGER DEFAULT 0,              -- 已联系用户数
    intervened_users            INTEGER DEFAULT 0,              -- 已干预用户数
    recovered_users             INTEGER DEFAULT 0,              -- 已挽回用户数
    churned_users               INTEGER DEFAULT 0,              -- 已流失用户数
    
    -- 效果统计
    intervention_success_rate   DECIMAL(5,2) DEFAULT 0.0,       -- 干预成功率
    avg_risk_score              DECIMAL(5,2) DEFAULT 0.0,       -- 平均风险评分
    
    -- 变化趋势 (相对于前一天)
    critical_change             INTEGER DEFAULT 0,              -- 严重风险用户变化
    high_risk_change            INTEGER DEFAULT 0,              -- 高风险用户变化
    medium_risk_change          INTEGER DEFAULT 0,              -- 中等风险用户变化
    low_risk_change             INTEGER DEFAULT 0,              -- 低风险用户变化
    
    -- 时间戳
    created_at                  DATETIME NOT NULL,
    updated_at                  DATETIME NOT NULL
);
```

### 4. churn_warning_churninterventionlog (干预记录表)

**用途**: 记录对风险用户采取的干预措施和效果跟踪

```sql
CREATE TABLE churn_warning_churninterventionlog (
    id                      BIGINT PRIMARY KEY AUTO_INCREMENT,
    prediction_id           BIGINT NOT NULL,                    -- 关联预测记录
    intervention_type       VARCHAR(20) NOT NULL,              -- 干预类型
    description             TEXT NOT NULL,                     -- 干预描述
    status                  VARCHAR(20) DEFAULT 'planned',     -- 执行状态
    
    -- 执行详情
    executor                VARCHAR(100),                      -- 执行人
    execution_date          DATETIME NULL,                     -- 执行时间
    completion_date         DATETIME NULL,                     -- 完成时间
    
    -- 效果评估
    effectiveness_score     INTEGER NULL,                      -- 效果评分(1-5)
    user_response           TEXT,                              -- 用户反应
    notes                   TEXT,                              -- 备注
    
    -- 时间戳
    created_at              DATETIME NOT NULL,
    updated_at              DATETIME NOT NULL,
    
    -- 外键约束
    FOREIGN KEY (prediction_id) REFERENCES churn_warning_churnprediction(id) ON DELETE CASCADE
);
```

**字段说明**:
- `intervention_type`: 干预类型 (`contact`, `gift`, `discount`, `service`, `event`, `other`)
- `status`: 执行状态 (`planned`, `executing`, `completed`, `failed`)
- `effectiveness_score`: 1-5分的效果评分
- `user_response`: 记录用户对干预措施的反应

### 5. churn_warning_churnwarningconfig (系统配置表)

**用途**: 存储系统级别的配置参数 (单例表)

```sql
CREATE TABLE churn_warning_churnwarningconfig (
    id                      BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- 预警阈值配置
    critical_threshold      DECIMAL(5,2) DEFAULT 80.0,         -- 严重风险阈值
    high_threshold          DECIMAL(5,2) DEFAULT 60.0,         -- 高风险阈值  
    medium_threshold        DECIMAL(5,2) DEFAULT 40.0,         -- 中等风险阈值
    
    -- 预测配置
    prediction_days         INTEGER DEFAULT 30,               -- 预测天数
    min_confidence_score    DECIMAL(3,2) DEFAULT 0.7,         -- 最低置信度
    
    -- 自动处理配置
    auto_contact_critical   BOOLEAN DEFAULT TRUE,             -- 自动联系严重风险用户
    auto_gift_threshold     DECIMAL(5,2) DEFAULT 85.0,        -- 自动赠送礼包阈值
    
    -- 统计配置
    stats_retention_days    INTEGER DEFAULT 90,               -- 统计数据保留天数
    
    -- 时间戳
    created_at              DATETIME NOT NULL,
    updated_at              DATETIME NOT NULL
);
```

## 🔗 关系图

### 表关系图

```mermaid
erDiagram
    big_r_overview_bigruser ||--o{ churn_warning_churnprediction : "一对一"
    big_r_overview_userloginrecord ||--o{ big_r_overview_bigruser : "用户登录记录"
    big_r_overview_userrechargerecord ||--o{ big_r_overview_bigruser : "用户充值记录"
    
    churn_warning_churnprediction ||--o{ churn_warning_churninterventionlog : "一对多"
    churn_warning_churnriskfactor ||--o{ churn_warning_churnprediction : "配置关联"
    churn_warning_churnwarningconfig ||--o{ churn_warning_churnprediction : "配置关联"
    
    big_r_overview_bigruser {
        bigint id PK
        bigint user_id UK
        string username
        string character_name
        decimal total_recharge
        datetime last_login_date
        datetime last_recharge_date
        int consecutive_login_days
    }
    
    churn_warning_churnprediction {
        bigint id PK
        bigint user_id FK
        string risk_level
        decimal risk_score
        decimal login_risk_score
        decimal recharge_risk_score
        date predicted_churn_date
        json risk_factors
        string status
    }
    
    churn_warning_churnriskfactor {
        bigint id PK
        string factor_type UK
        decimal weight
        decimal threshold_value
        boolean is_active
    }
    
    churn_warning_churninterventionlog {
        bigint id PK
        bigint prediction_id FK
        string intervention_type
        string status
        int effectiveness_score
    }
    
    churn_warning_churnwarningstats {
        bigint id PK
        date stat_date UK
        int critical_users
        int high_risk_users
        int medium_risk_users
        decimal avg_risk_score
    }
    
    churn_warning_churnwarningconfig {
        bigint id PK
        decimal critical_threshold
        decimal high_threshold
        int prediction_days
        boolean auto_contact_critical
    }
```

## 📈 索引设计

### 主要索引

```sql
-- churn_warning_churnprediction 表索引
CREATE INDEX idx_churn_prediction_risk_level ON churn_warning_churnprediction(risk_level);
CREATE INDEX idx_churn_prediction_risk_score ON churn_warning_churnprediction(risk_score);
CREATE INDEX idx_churn_prediction_status ON churn_warning_churnprediction(status);
CREATE INDEX idx_churn_prediction_created_at ON churn_warning_churnprediction(created_at);
CREATE INDEX idx_churn_prediction_predicted_date ON churn_warning_churnprediction(predicted_churn_date);

-- churn_warning_churninterventionlog 表索引
CREATE INDEX idx_intervention_prediction_type ON churn_warning_churninterventionlog(prediction_id, intervention_type);
CREATE INDEX idx_intervention_status ON churn_warning_churninterventionlog(status);
CREATE INDEX idx_intervention_execution_date ON churn_warning_churninterventionlog(execution_date);

-- churn_warning_churnwarningstats 表索引
CREATE INDEX idx_warning_stats_date ON churn_warning_churnwarningstats(stat_date);

-- churn_warning_churnriskfactor 表索引  
CREATE INDEX idx_risk_factor_active ON churn_warning_churnriskfactor(is_active);
```

### 复合索引

```sql
-- 用于按风险等级和评分排序的复合索引
CREATE INDEX idx_prediction_level_score ON churn_warning_churnprediction(risk_level, risk_score DESC);

-- 用于按状态和更新时间查询的复合索引
CREATE INDEX idx_prediction_status_updated ON churn_warning_churnprediction(status, updated_at DESC);

-- 用于统计查询的复合索引
CREATE INDEX idx_stats_date_levels ON churn_warning_churnwarningstats(stat_date, critical_users, high_risk_users);
```

## 🔄 数据流转

### 风险评分计算流程

```mermaid
flowchart TD
    A[定时任务触发] --> B[获取所有大R用户]
    B --> C[遍历每个用户]
    C --> D[从 UserLoginRecord 计算登录风险]
    C --> E[从 UserRechargeRecord 计算充值风险]  
    C --> F[计算战斗风险]
    C --> G[计算社交风险]
    
    D --> H[合并各维度评分]
    E --> H
    F --> H
    G --> H
    
    H --> I[从 ChurnRiskFactor 获取权重配置]
    I --> J[计算加权综合评分]
    J --> K[确定风险等级]
    K --> L[预测流失日期]
    L --> M[生成风险因素和建议措施]
    M --> N[保存到 ChurnPrediction 表]
    
    N --> O[更新 ChurnWarningStats 统计]
```

### 干预处理流程

```mermaid
flowchart TD
    A[用户操作触发] --> B{操作类型}
    B -->|单用户联系| C[创建干预记录]
    B -->|批量联系| D[批量创建干预记录]
    B -->|赠送礼包| E[创建礼包干预记录]
    
    C --> F[更新预测状态为 contacted]
    D --> G[批量更新预测状态]
    E --> H[更新预测状态为 intervened]
    
    F --> I[记录到 ChurnInterventionLog]
    G --> I
    H --> I
    
    I --> J[后续跟踪效果]
    J --> K[更新效果评分]
    K --> L[可能更新预测状态为 recovered]
```

## ⚡ 性能优化

### 查询优化建议

1. **分页查询优化**
```sql
-- 优化用户列表分页查询
SELECT cp.*, bu.character_name, bu.server_name 
FROM churn_warning_churnprediction cp
JOIN big_r_overview_bigruser bu ON cp.user_id = bu.id
WHERE cp.risk_level = 'critical'
ORDER BY cp.risk_score DESC, cp.updated_at DESC
LIMIT 20 OFFSET 0;
```

2. **统计查询优化**
```sql
-- 优化风险统计查询
SELECT 
    COUNT(CASE WHEN risk_level = 'critical' THEN 1 END) as critical_count,
    COUNT(CASE WHEN risk_level = 'high' THEN 1 END) as high_count,
    COUNT(CASE WHEN risk_level = 'medium' THEN 1 END) as medium_count,
    COUNT(CASE WHEN risk_level = 'low' THEN 1 END) as low_count
FROM churn_warning_churnprediction
WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 1 DAY);
```

### 数据清理策略

1. **定期清理历史数据**
```sql
-- 清理90天前的统计数据
DELETE FROM churn_warning_churnwarningstats 
WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);

-- 清理已完成的干预记录（保留6个月）
DELETE FROM churn_warning_churninterventionlog 
WHERE status = 'completed' 
AND completion_date < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

2. **数据归档策略**
```sql
-- 创建历史数据归档表
CREATE TABLE churn_warning_churnprediction_archive LIKE churn_warning_churnprediction;

-- 归档3个月前的预测数据
INSERT INTO churn_warning_churnprediction_archive 
SELECT * FROM churn_warning_churnprediction 
WHERE updated_at < DATE_SUB(NOW(), INTERVAL 3 MONTH);
```

### 缓存策略

1. **Redis缓存热点数据**
```python
# 缓存风险统计数据（5分钟过期）
cache_key = f"churn_stats_{date.today()}"
redis_client.setex(cache_key, 300, json.dumps(stats_data))

# 缓存风险因子配置（1小时过期）
cache_key = "risk_factors_config"
redis_client.setex(cache_key, 3600, json.dumps(factors_data))
```

2. **数据库连接池优化**
```python
# Django settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
        },
        'CONN_MAX_AGE': 600,  # 连接池最大存活时间
    }
}
```

---

*文档版本: v1.0*  
*最后更新: 2025-08-06*  
*维护人员: VIPManager 开发团队*