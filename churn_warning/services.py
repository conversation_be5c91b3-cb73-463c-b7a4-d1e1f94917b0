from django.db.models import Count, Q, Avg
from django.utils import timezone
from datetime import date, timedelta, datetime
from typing import Dict, List, Optional
import logging

from .models import (
    ChurnPrediction, ChurnWarningStats, ChurnRiskFactor, 
    ChurnInterventionLog, ChurnWarningConfig
)
from big_r_overview.models import BigRUser, UserLoginRecord, UserRechargeRecord

logger = logging.getLogger(__name__)


class ChurnPredictionService:
    """流失预测服务"""
    
    @classmethod
    def calculate_user_risk_scores(cls, user_id: Optional[int] = None) -> Dict:
        """计算用户风险评分"""
        try:
            # 获取配置
            config = ChurnWarningConfig.get_config()
            
            # 确定要处理的用户
            if user_id:
                users = BigRUser.objects.filter(id=user_id)
            else:
                users = BigRUser.objects.all()
            
            updated_count = 0
            
            for user in users:
                # 计算各维度风险评分
                login_score = cls._calculate_login_risk(user)
                recharge_score = cls._calculate_recharge_risk(user)
                activity_score = cls._calculate_activity_risk(user)
                
                # 获取或创建预测记录
                prediction, created = ChurnPrediction.objects.get_or_create(
                    user=user,
                    defaults={
                        'risk_level': 'low',
                        'risk_score': 0.0,
                    }
                )
                
                # 更新各维度评分
                prediction.login_risk_score = login_score
                prediction.recharge_risk_score = recharge_score
                prediction.activity_risk_score = activity_score
                
                # 计算综合风险评分
                prediction.calculate_risk_score()
                
                # 计算预测流失日期
                prediction.predicted_churn_date = cls._predict_churn_date(user, prediction.risk_score)
                
                # 生成风险因素标签
                prediction.risk_factors = cls._generate_risk_factors(user, {
                    'login': login_score,
                    'recharge': recharge_score,
                    'activity': activity_score
                })
                
                # 生成建议措施
                prediction.suggested_actions = cls._generate_suggested_actions(prediction)
                
                prediction.save()
                updated_count += 1
            
            logger.info(f"成功更新 {updated_count} 位用户的风险评分")
            
            return {
                'success': True,
                'updated_count': updated_count,
                'message': f'成功更新 {updated_count} 位用户的风险评分'
            }
            
        except Exception as e:
            logger.error(f"计算用户风险评分失败: {str(e)}")
            return {
                'success': False,
                'message': f'计算风险评分失败: {str(e)}'
            }
    
    @classmethod
    def _calculate_login_risk(cls, user: BigRUser) -> float:
        """计算登录风险评分"""
        try:
            now = timezone.now()
            thirty_days_ago = now - timedelta(days=30)
            
            # 最近30天登录记录
            recent_logins = UserLoginRecord.objects.filter(
                user=user,
                login_date__gte=thirty_days_ago.date()
            ).count()
            
            # 计算登录频率风险
            expected_logins = 20  # 期望30天内登录20次
            login_frequency_score = max(0, (expected_logins - recent_logins) / expected_logins * 100)
            
            # 距离上次登录天数风险
            if user.last_login_date:
                days_since_login = (now - user.last_login_date).days
                login_recency_score = min(100, days_since_login * 5)  # 每天+5分
            else:
                login_recency_score = 100
            
            # 连续登录天数风险（连续登录天数越少风险越高）
            consecutive_score = max(0, (10 - user.consecutive_login_days) * 10)
            
            # 综合登录风险评分
            login_risk = (login_frequency_score * 0.4 + 
                         login_recency_score * 0.4 + 
                         consecutive_score * 0.2)
            
            return min(100, login_risk)
            
        except Exception as e:
            logger.error(f"计算用户 {user.id} 登录风险失败: {str(e)}")
            return 0.0
    
    @classmethod
    def _calculate_recharge_risk(cls, user: BigRUser) -> float:
        """计算充值风险评分"""
        try:
            now = timezone.now()
            thirty_days_ago = now - timedelta(days=30)
            
            # 最近30天充值记录
            recent_recharges = UserRechargeRecord.objects.filter(
                user=user,
                status='success',
                paid_at__gte=thirty_days_ago
            )
            
            recent_count = recent_recharges.count()
            recent_amount = sum([r.amount for r in recent_recharges])
            
            # 充值频率风险
            if user.first_recharge_date:
                total_days = (now.date() - user.first_recharge_date.date()).days
                if total_days > 0:
                    avg_monthly_recharges = (UserRechargeRecord.objects.filter(
                        user=user, status='success'
                    ).count() / total_days) * 30
                    
                    frequency_risk = max(0, (avg_monthly_recharges - recent_count) / max(avg_monthly_recharges, 1) * 100)
                else:
                    frequency_risk = 0
            else:
                frequency_risk = 100  # 从未充值
            
            # 距离上次充值天数风险
            if user.last_recharge_date:
                days_since_recharge = (now.date() - user.last_recharge_date.date()).days
                recency_risk = min(100, days_since_recharge * 3)  # 每天+3分
            else:
                recency_risk = 100
            
            # 充值金额下降风险
            if recent_amount > 0 and user.total_recharge > 0:
                avg_monthly_amount = float(user.total_recharge) / max(1, (now.date() - user.first_recharge_date.date()).days / 30)
                amount_risk = max(0, (avg_monthly_amount - float(recent_amount)) / avg_monthly_amount * 100)
            else:
                amount_risk = 80
            
            # 综合充值风险评分
            recharge_risk = (frequency_risk * 0.3 + 
                           recency_risk * 0.4 + 
                           amount_risk * 0.3)
            
            return min(100, recharge_risk)
            
        except Exception as e:
            logger.error(f"计算用户 {user.id} 充值风险失败: {str(e)}")
            return 0.0
    
    @classmethod
    def _calculate_activity_risk(cls, user: BigRUser) -> float:
        """计算活跃风险评分"""
        try:
            now = timezone.now()
            seven_days_ago = now - timedelta(days=7)
            thirty_days_ago = now - timedelta(days=30)
            
            # 注意：这里需要根据实际的用户行为数据表进行查询
            # 目前使用模拟计算，实际项目中需要连接真实的在线时长和任务完成度数据
            
            # 模拟计算在线时长风险 (权重: 40%)
            # 实际应该查询用户的在线时长记录表
            recent_7days_online_time = cls._get_recent_online_time(user, 7)  # 近7天平均在线时长（分钟/天）
            historical_30days_online_time = cls._get_historical_online_time(user, 30)  # 历史30天平均在线时长
            
            if historical_30days_online_time > 0:
                online_time_risk = max(0, (1 - recent_7days_online_time / historical_30days_online_time) * 100)
            else:
                online_time_risk = 50  # 无历史数据时使用中等风险
            
            # 模拟计算活跃度风险 (权重: 60%)
            # 实际应该查询用户的任务完成记录表
            recent_7days_task_completion = cls._get_recent_task_completion(user, 7)  # 近7天平均任务完成度(%)
            historical_30days_task_completion = cls._get_historical_task_completion(user, 30)  # 历史30天平均任务完成度
            
            if historical_30days_task_completion > 0:
                task_completion_risk = max(0, (1 - recent_7days_task_completion / historical_30days_task_completion) * 100)
            else:
                task_completion_risk = 50  # 无历史数据时使用中等风险
            
            # 特殊情况处理
            # 连续3天未登录直接标记为高风险
            if user.last_login_date:
                days_since_last_login = (now.date() - user.last_login_date.date()).days if hasattr(user.last_login_date, 'date') else (now.date() - user.last_login_date).days
                if days_since_last_login >= 3:
                    online_time_risk = 100
                    task_completion_risk = 100
            
            # 综合活跃风险评分
            activity_risk = online_time_risk * 0.4 + task_completion_risk * 0.6
            
            return min(100, activity_risk)
            
        except Exception as e:
            logger.error(f"计算用户 {user.id} 活跃风险失败: {str(e)}")
            return 0.0
    
    @classmethod
    def _get_recent_online_time(cls, user: BigRUser, days: int) -> float:
        """获取近期平均在线时长（分钟/天）"""
        # TODO: 实际实现中需要查询用户在线时长记录表
        # 这里使用模拟数据
        import random
        
        # 模拟：VIP等级越高，在线时长越长
        base_time = 60 + user.vip_level * 10  # 基础在线时长
        
        # 根据用户登录活跃度调整
        if hasattr(user, 'consecutive_login_days') and user.consecutive_login_days:
            if user.consecutive_login_days < 3:
                return base_time * 0.3  # 连续登录少，在线时长短
            elif user.consecutive_login_days > 10:
                return base_time * 1.2  # 连续登录多，在线时长长
        
        return base_time + random.uniform(-20, 20)
    
    @classmethod
    def _get_historical_online_time(cls, user: BigRUser, days: int) -> float:
        """获取历史平均在线时长（分钟/天）"""
        # TODO: 实际实现中需要查询用户历史在线时长记录
        # 这里使用模拟数据，通常比近期数据稍高
        recent_time = cls._get_recent_online_time(user, days)
        return recent_time * 1.2
    
    @classmethod
    def _get_recent_task_completion(cls, user: BigRUser, days: int) -> float:
        """获取近期平均任务完成度(%)"""
        # TODO: 实际实现中需要查询用户任务完成记录表
        # 这里使用模拟数据
        import random
        
        # 模拟：根据用户活跃度生成任务完成度
        base_completion = 40 + user.vip_level * 5  # VIP等级越高，任务完成度越高
        
        # 根据最后登录时间调整
        if user.last_login_date:
            now = timezone.now()
            days_since_login = (now.date() - user.last_login_date.date()).days if hasattr(user.last_login_date, 'date') else (now.date() - user.last_login_date).days
            if days_since_login > 3:
                return base_completion * 0.2  # 多天未登录，任务完成度很低
            elif days_since_login == 0:
                return base_completion * 1.1  # 今天登录了，任务完成度正常
        
        return min(100, base_completion + random.uniform(-15, 15))
    
    @classmethod
    def _get_historical_task_completion(cls, user: BigRUser, days: int) -> float:
        """获取历史平均任务完成度(%)"""
        # TODO: 实际实现中需要查询用户历史任务完成记录
        # 这里使用模拟数据，通常比近期数据稍高
        recent_completion = cls._get_recent_task_completion(user, days)
        return min(100, recent_completion * 1.15)
    
    @classmethod
    def _predict_churn_date(cls, user: BigRUser, risk_score: float) -> Optional[date]:
        """预测流失日期"""
        if risk_score < 40:
            return None
        
        # 根据风险评分预测流失天数
        if risk_score >= 80:
            days_to_churn = 7  # 严重风险：7天内
        elif risk_score >= 60:
            days_to_churn = 14  # 高风险：14天内
        else:
            days_to_churn = 30  # 中等风险：30天内
        
        return timezone.now().date() + timedelta(days=days_to_churn)
    
    @classmethod
    def _generate_risk_factors(cls, user: BigRUser, scores: Dict[str, float]) -> List[Dict]:
        """生成风险因素标签"""
        factors = []
        
        if scores['login'] >= 60:
            factors.append({
                'type': 'login',
                'text': '登录风险',
                'score': scores['login'],
                'is_primary': scores['login'] >= 80
            })
        
        if scores['recharge'] >= 60:
            factors.append({
                'type': 'recharge',
                'text': '充值风险',
                'score': scores['recharge'],
                'is_primary': scores['recharge'] >= 80
            })
        
        if scores['activity'] >= 60:
            factors.append({
                'type': 'activity',
                'text': '活跃风险',
                'score': scores['activity'],
                'is_primary': scores['activity'] >= 80
            })
        
        return factors
    
    @classmethod
    def _generate_suggested_actions(cls, prediction: ChurnPrediction) -> List[Dict]:
        """生成建议措施"""
        actions = []
        
        if prediction.risk_level == 'critical':
            actions.extend([
                {'type': 'contact', 'text': '立即联系用户', 'priority': 'high', 'reason': '了解流失原因'},
                {'type': 'gift', 'text': '赠送高价值礼包', 'priority': 'high', 'reason': '提供即时价值'},
                {'type': 'service', 'text': '安排专属客服', 'priority': 'high', 'reason': '专人跟进服务'},
                {'type': 'task_reward', 'text': '发放任务奖励', 'priority': 'high', 'reason': '激励活跃参与'}
            ])
        elif prediction.risk_level == 'high':
            actions.extend([
                {'type': 'contact', 'text': '主动联系用户', 'priority': 'medium', 'reason': '主动关怀沟通'},
                {'type': 'discount', 'text': '提供充值折扣', 'priority': 'medium', 'reason': '降低付费门槛'},
                {'type': 'event', 'text': '邀请参加活动', 'priority': 'medium', 'reason': '重新激发兴趣'},
                {'type': 'task_guide', 'text': '任务完成指导', 'priority': 'medium', 'reason': '提升参与度'}
            ])
        elif prediction.risk_level == 'medium':
            actions.extend([
                {'type': 'gift', 'text': '赠送小额礼包', 'priority': 'low', 'reason': '维护用户关系'},
                {'type': 'event', 'text': '推荐适合活动', 'priority': 'low', 'reason': '保持游戏兴趣'},
                {'type': 'monitor', 'text': '持续监控状态', 'priority': 'low', 'reason': '防止风险升级'}
            ])
        else:  # low risk
            actions.extend([
                {'type': 'maintenance', 'text': '正常维护', 'priority': 'low', 'reason': '保持现状'},
                {'type': 'reward', 'text': '忠实用户奖励', 'priority': 'low', 'reason': '增强归属感'}
            ])
        
        # 根据具体风险因子添加针对性措施
        for factor in prediction.risk_factors:
            if factor.get('type') == 'activity' and factor.get('score', 0) >= 70:
                actions.append({
                    'type': 'activity_boost', 
                    'text': '活跃度激励计划', 
                    'priority': 'high',
                    'reason': '活跃度大幅下降，需要专门激励'
                })
            elif factor.get('type') == 'recharge' and factor.get('score', 0) >= 80:
                actions.append({
                    'type': 'payment_support', 
                    'text': '充值问题咨询', 
                    'priority': 'high',
                    'reason': '充值行为异常，可能存在支付问题'
                })
        
        return actions


class ChurnWarningService:
    """流失预警业务服务"""
    
    @classmethod
    def get_risk_stats(cls) -> Dict:
        """获取风险统计数据"""
        try:
            today = date.today()
            yesterday = today - timedelta(days=1)
            
            # 今日统计
            today_stats = cls._get_daily_stats(today)
            
            # 昨日统计（用于计算变化）
            yesterday_stats = cls._get_daily_stats(yesterday)
            
            # 计算变化
            changes = {
                'critical_change': today_stats['critical'] - yesterday_stats.get('critical', 0),
                'high_change': today_stats['high'] - yesterday_stats.get('high', 0),
                'medium_change': today_stats['medium'] - yesterday_stats.get('medium', 0),
                'low_change': today_stats['low'] - yesterday_stats.get('low', 0),
            }
            
            # 格式化变化显示
            formatted_changes = {}
            for key, value in changes.items():
                if value > 0:
                    formatted_changes[key.replace('_change', '')] = f"+{value}"
                elif value < 0:
                    formatted_changes[key.replace('_change', '')] = str(value)
                else:
                    formatted_changes[key.replace('_change', '')] = "0"
            
            return {
                'success': True,
                'data': {
                    'critical': {
                        'value': today_stats['critical'],
                        'change': formatted_changes['critical']
                    },
                    'high': {
                        'value': today_stats['high'],
                        'change': formatted_changes['high']
                    },
                    'medium': {
                        'value': today_stats['medium'],
                        'change': formatted_changes['medium']
                    },
                    'low': {
                        'value': today_stats['low'],
                        'change': formatted_changes['low']
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"获取风险统计数据失败: {str(e)}")
            return {
                'success': False,
                'message': f'获取统计数据失败: {str(e)}'
            }
    
    @classmethod
    def _get_daily_stats(cls, stat_date: date) -> Dict[str, int]:
        """获取指定日期的统计数据"""
        # 从数据库获取当天的预测数据
        predictions = ChurnPrediction.objects.filter(
            updated_at__date=stat_date
        )
        
        stats = {
            'critical': predictions.filter(risk_level='critical').count(),
            'high': predictions.filter(risk_level='high').count(), 
            'medium': predictions.filter(risk_level='medium').count(),
            'low': predictions.filter(risk_level='low').count(),
        }
        
        return stats
    
    @classmethod
    def get_users_list(cls, page: int = 1, page_size: int = 20, **filters) -> Dict:
        """获取用户列表"""
        try:
            # 构建查询条件
            queryset = ChurnPrediction.objects.select_related('user').all()
            
            # 风险等级筛选
            if filters.get('risk_level') and filters['risk_level'] != 'all':
                queryset = queryset.filter(risk_level=filters['risk_level'])
            
            # 搜索筛选
            search_query = filters.get('search', '').strip()
            if search_query:
                queryset = queryset.filter(
                    Q(user__character_name__icontains=search_query) |
                    Q(user__username__icontains=search_query) |
                    Q(user__user_id__icontains=search_query)
                )
            
            # 服务器筛选
            if filters.get('server'):
                queryset = queryset.filter(user__server_name=filters['server'])
            
            # 排序
            queryset = queryset.order_by('-risk_score', '-updated_at')
            
            # 分页
            total_count = queryset.count()
            total_pages = (total_count + page_size - 1) // page_size
            
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            
            predictions = queryset[start_index:end_index]
            
            # 格式化用户数据
            users_data = []
            for prediction in predictions:
                user = prediction.user
                users_data.append({
                    'id': user.user_id,
                    'username': user.username,
                    'character_name': user.character_name,
                    'server_name': user.server_name,
                    'vip_level': user.vip_level,
                    'vip_level_name': user.vip_level_name,
                    'risk_level': prediction.risk_level,
                    'risk_score': int(prediction.risk_score),
                    'total_recharge': float(user.total_recharge),
                    'last_login': cls._format_last_activity(user.last_login_date),
                    'last_recharge': cls._format_last_activity(user.last_recharge_date),
                    'risk_factors': prediction.risk_factors,
                    'predicted_churn_date': prediction.predicted_churn_date,
                    'status': prediction.status
                })
            
            return {
                'success': True,
                'data': {
                    'users': users_data,
                    'pagination': {
                        'current_page': page,
                        'total_pages': total_pages,
                        'total_count': total_count,
                        'page_size': page_size
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"获取用户列表失败: {str(e)}")
            return {
                'success': False,
                'message': f'获取用户列表失败: {str(e)}'
            }
    
    @classmethod
    def _format_last_activity(cls, activity_date) -> str:
        """格式化最后活动时间"""
        if not activity_date:
            return "从未"
        
        if isinstance(activity_date, date):
            activity_datetime = datetime.combine(activity_date, datetime.min.time())
            activity_datetime = timezone.make_aware(activity_datetime)
        else:
            activity_datetime = activity_date
        
        now = timezone.now()
        diff = now - activity_datetime
        
        if diff.days == 0:
            return "今天"
        elif diff.days == 1:
            return "1天前"
        elif diff.days < 30:
            return f"{diff.days}天前"
        else:
            months = diff.days // 30
            return f"{months}个月前"
    
    @classmethod
    def batch_contact_users(cls, user_ids: List[int]) -> Dict:
        """批量联系用户"""
        try:
            contacted_count = 0
            
            for user_id in user_ids:
                try:
                    prediction = ChurnPrediction.objects.get(user__user_id=user_id)
                    
                    # 创建干预记录
                    ChurnInterventionLog.objects.create(
                        prediction=prediction,
                        intervention_type='contact',
                        description=f'批量联系用户 {prediction.user.character_name}',
                        status='planned',
                        executor='系统'
                    )
                    
                    # 更新预测状态
                    prediction.status = 'contacted'
                    prediction.save()
                    
                    contacted_count += 1
                    
                except ChurnPrediction.DoesNotExist:
                    logger.warning(f"用户 {user_id} 的预测记录不存在")
                    continue
            
            return {
                'success': True,
                'contacted_count': contacted_count,
                'message': f'成功联系 {contacted_count} 位用户'
            }
            
        except Exception as e:
            logger.error(f"批量联系用户失败: {str(e)}")
            return {
                'success': False,
                'message': f'批量联系失败: {str(e)}'
            }
    
    @classmethod
    def batch_send_gifts(cls, user_ids: List[int]) -> Dict:
        """批量赠送礼包"""
        try:
            gift_count = 0
            
            for user_id in user_ids:
                try:
                    prediction = ChurnPrediction.objects.get(user__user_id=user_id)
                    
                    # 创建干预记录
                    ChurnInterventionLog.objects.create(
                        prediction=prediction,
                        intervention_type='gift',
                        description=f'批量赠送礼包给用户 {prediction.user.character_name}',
                        status='planned',
                        executor='系统'
                    )
                    
                    gift_count += 1
                    
                except ChurnPrediction.DoesNotExist:
                    logger.warning(f"用户 {user_id} 的预测记录不存在")
                    continue
            
            return {
                'success': True,
                'gift_count': gift_count,
                'message': f'成功为 {gift_count} 位用户准备礼包'
            }
            
        except Exception as e:
            logger.error(f"批量赠送礼包失败: {str(e)}")
            return {
                'success': False,
                'message': f'批量赠送失败: {str(e)}'
            }
    
    @classmethod 
    def update_daily_stats(cls, stat_date: Optional[date] = None) -> Dict:
        """更新每日统计数据"""
        if not stat_date:
            stat_date = date.today()
        
        try:
            # 获取当日各风险等级用户数量
            predictions_today = ChurnPrediction.objects.filter(
                updated_at__date=stat_date
            )
            
            critical_count = predictions_today.filter(risk_level='critical').count()
            high_count = predictions_today.filter(risk_level='high').count()
            medium_count = predictions_today.filter(risk_level='medium').count()
            low_count = predictions_today.filter(risk_level='low').count()
            
            # 获取处理统计
            interventions_today = ChurnInterventionLog.objects.filter(
                created_at__date=stat_date
            )
            
            contacted_count = interventions_today.filter(
                intervention_type='contact',
                status='completed'
            ).count()
            
            intervened_count = interventions_today.filter(
                status='completed'
            ).count()
            
            recovered_count = predictions_today.filter(
                status='recovered'
            ).count()
            
            churned_count = predictions_today.filter(
                status='churned'
            ).count()
            
            # 计算平均风险评分
            avg_risk_score = predictions_today.aggregate(
                avg_score=Avg('risk_score')
            )['avg_score'] or 0.0
            
            # 获取昨日数据计算变化
            yesterday = stat_date - timedelta(days=1)
            try:
                yesterday_stats = ChurnWarningStats.objects.get(stat_date=yesterday)
                critical_change = critical_count - yesterday_stats.critical_users
                high_change = high_count - yesterday_stats.high_risk_users
                medium_change = medium_count - yesterday_stats.medium_risk_users
                low_change = low_count - yesterday_stats.low_risk_users
            except ChurnWarningStats.DoesNotExist:
                critical_change = high_change = medium_change = low_change = 0
            
            # 更新或创建统计记录
            stats, created = ChurnWarningStats.objects.update_or_create(
                stat_date=stat_date,
                defaults={
                    'critical_users': critical_count,
                    'high_risk_users': high_count,
                    'medium_risk_users': medium_count,
                    'low_risk_users': low_count,
                    'contacted_users': contacted_count,
                    'intervened_users': intervened_count,
                    'recovered_users': recovered_count,
                    'churned_users': churned_count,
                    'avg_risk_score': avg_risk_score,
                    'critical_change': critical_change,
                    'high_risk_change': high_change,
                    'medium_risk_change': medium_change,
                    'low_risk_change': low_change,
                }
            )
            
            # 计算干预成功率
            stats.calculate_intervention_success_rate()
            stats.save()
            
            return {
                'success': True,
                'message': f'成功更新 {stat_date} 的统计数据'
            }
            
        except Exception as e:
            logger.error(f"更新每日统计数据失败: {str(e)}")
            return {
                'success': False,
                'message': f'更新统计数据失败: {str(e)}'
            }