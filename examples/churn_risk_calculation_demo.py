#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流失预警中心 - 风险计算演示脚本

本脚本演示了3个典型用户的风险计算过程，
可以独立运行，无需Django环境。

使用方法：
    python examples/churn_risk_calculation_demo.py
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple
import json


class ChurnRiskCalculator:
    """流失风险计算器"""
    
    def __init__(self):
        # 风险因子权重配置
        self.weights = {
            'login_risk': 0.25,      # 登录风险权重 25%
            'recharge_risk': 0.30,   # 充值风险权重 30%
            'battle_risk': 0.10,     # 战斗风险权重 10%
            'social_risk': 0.15,     # 社交风险权重 15%
            'other_risk': 0.20       # 其他风险权重 20%
        }
        
        # 风险等级阈值
        self.thresholds = {
            'critical': 80.0,    # 严重风险阈值
            'high': 60.0,        # 高风险阈值
            'medium': 40.0,      # 中等风险阈值
        }
    
    def calculate_login_risk(self, user_data: Dict) -> float:
        """计算登录风险评分"""
        # 登录频率风险 (权重: 40%)
        expected_logins = 20  # 期望30天内登录20次
        actual_logins = user_data['recent_logins']
        frequency_risk = max(0, (expected_logins - actual_logins) / expected_logins * 100)
        
        # 登录间隔风险 (权重: 40%)
        days_since_login = user_data['days_since_login']
        recency_risk = min(100, days_since_login * 5)
        
        # 连续登录风险 (权重: 20%)
        expected_consecutive = 10  # 期望连续登录10天
        actual_consecutive = user_data['consecutive_login_days']
        consecutive_risk = max(0, (expected_consecutive - actual_consecutive) * 10)
        
        # 综合登录风险
        login_risk = (frequency_risk * 0.4 + recency_risk * 0.4 + consecutive_risk * 0.2)
        
        return min(100, login_risk)
    
    def calculate_recharge_risk(self, user_data: Dict) -> float:
        """计算充值风险评分"""
        # 充值频率风险 (权重: 30%)
        historical_avg = user_data['historical_monthly_recharge_count']
        recent_count = user_data['recent_recharge_count']
        frequency_risk = max(0, (historical_avg - recent_count) / historical_avg * 100) if historical_avg > 0 else 100
        
        # 充值间隔风险 (权重: 40%)
        days_since_recharge = user_data['days_since_recharge']
        recency_risk = min(100, days_since_recharge * 3)
        
        # 充值金额风险 (权重: 30%)
        historical_avg_amount = user_data['historical_monthly_recharge_amount']
        recent_amount = user_data['recent_recharge_amount']
        amount_risk = max(0, (historical_avg_amount - recent_amount) / historical_avg_amount * 100) if historical_avg_amount > 0 else 100
        
        # 综合充值风险
        recharge_risk = (frequency_risk * 0.3 + recency_risk * 0.4 + amount_risk * 0.3)
        
        return min(100, recharge_risk)
    
    def calculate_comprehensive_risk(self, user_data: Dict) -> Dict:
        """计算综合风险评分"""
        # 计算各维度风险评分
        login_risk = self.calculate_login_risk(user_data)
        recharge_risk = self.calculate_recharge_risk(user_data)
        battle_risk = user_data.get('battle_risk', 50)  # 模拟数据
        social_risk = user_data.get('social_risk', 50)  # 模拟数据
        other_risk = user_data.get('other_risk', 50)    # 模拟数据
        
        # 计算综合风险评分
        comprehensive_risk = (
            login_risk * self.weights['login_risk'] +
            recharge_risk * self.weights['recharge_risk'] +
            battle_risk * self.weights['battle_risk'] +
            social_risk * self.weights['social_risk'] +
            other_risk * self.weights['other_risk']
        )
        
        # 确定风险等级
        if comprehensive_risk >= self.thresholds['critical']:
            risk_level = 'critical'
            risk_label = '🔴 严重风险'
            predicted_days = 7
        elif comprehensive_risk >= self.thresholds['high']:
            risk_level = 'high'
            risk_label = '🟠 高风险'
            predicted_days = 14
        elif comprehensive_risk >= self.thresholds['medium']:
            risk_level = 'medium'
            risk_label = '🔵 中等风险'
            predicted_days = 30
        else:
            risk_level = 'low'
            risk_label = '🟢 低风险'
            predicted_days = None
        
        # 计算预测流失日期
        predicted_churn_date = None
        if predicted_days:
            predicted_churn_date = (date.today() + timedelta(days=predicted_days)).strftime('%Y-%m-%d')
        
        # 生成风险因素标签
        risk_factors = []
        if login_risk >= 60:
            risk_factors.append({
                'type': 'login',
                'text': '登录风险',
                'score': round(login_risk, 2),
                'is_primary': login_risk >= 80
            })
        
        if recharge_risk >= 60:
            risk_factors.append({
                'type': 'recharge',
                'text': '充值风险',
                'score': round(recharge_risk, 2),
                'is_primary': recharge_risk >= 80
            })
        
        if battle_risk >= 60:
            risk_factors.append({
                'type': 'battle',
                'text': '战斗风险',
                'score': round(battle_risk, 2),
                'is_primary': battle_risk >= 80
            })
        
        if social_risk >= 60:
            risk_factors.append({
                'type': 'social',
                'text': '社交风险',
                'score': round(social_risk, 2),
                'is_primary': social_risk >= 80
            })
        
        # 生成建议措施
        suggested_actions = self._generate_suggestions(risk_level, comprehensive_risk)
        
        return {
            'user_id': user_data['user_id'],
            'character_name': user_data['character_name'],
            'vip_level': user_data['vip_level'],
            'comprehensive_risk_score': round(comprehensive_risk, 2),
            'risk_level': risk_level,
            'risk_label': risk_label,
            'predicted_churn_date': predicted_churn_date,
            'dimension_scores': {
                'login_risk': round(login_risk, 2),
                'recharge_risk': round(recharge_risk, 2),
                'battle_risk': round(battle_risk, 2),
                'social_risk': round(social_risk, 2),
                'other_risk': round(other_risk, 2)
            },
            'weight_contributions': {
                'login_contribution': round(login_risk * self.weights['login_risk'], 2),
                'recharge_contribution': round(recharge_risk * self.weights['recharge_risk'], 2),
                'battle_contribution': round(battle_risk * self.weights['battle_risk'], 2),
                'social_contribution': round(social_risk * self.weights['social_risk'], 2),
                'other_contribution': round(other_risk * self.weights['other_risk'], 2)
            },
            'risk_factors': risk_factors,
            'suggested_actions': suggested_actions
        }
    
    def _generate_suggestions(self, risk_level: str, risk_score: float) -> List[Dict]:
        """生成建议措施"""
        suggestions = []
        
        if risk_level == 'critical':
            suggestions.extend([
                {'type': 'contact', 'text': '立即联系用户', 'priority': 'high'},
                {'type': 'gift', 'text': '赠送高价值礼包', 'priority': 'high'},
                {'type': 'service', 'text': '安排专属客服', 'priority': 'high'}
            ])
        elif risk_level == 'high':
            suggestions.extend([
                {'type': 'contact', 'text': '主动联系用户', 'priority': 'medium'},
                {'type': 'discount', 'text': '提供充值折扣', 'priority': 'medium'},
                {'type': 'event', 'text': '邀请参加活动', 'priority': 'medium'}
            ])
        elif risk_level == 'medium':
            suggestions.extend([
                {'type': 'gift', 'text': '赠送礼包', 'priority': 'low'},
                {'type': 'event', 'text': '推荐适合活动', 'priority': 'low'}
            ])
        else:
            suggestions.append({
                'type': 'maintenance', 'text': '正常维护关系', 'priority': 'low'
            })
        
        return suggestions


def create_sample_users() -> List[Dict]:
    """创建示例用户数据"""
    users = [
        {
            # 角色一：高风险流失用户 - 张三
            'user_id': 10001,
            'character_name': '霸王张三',
            'vip_level': 8,
            'registration_days': 204,  # 注册7个月
            'total_recharge': 25000,
            'total_recharge_count': 15,
            
            # 登录相关数据
            'recent_logins': 8,           # 最近30天登录8次
            'days_since_login': 5,        # 距上次登录5天
            'consecutive_login_days': 2,   # 连续登录2天
            
            # 充值相关数据
            'historical_monthly_recharge_count': 2.2,  # 历史月均充值2.2次
            'recent_recharge_count': 0,                # 最近30天充值0次
            'days_since_recharge': 22,                 # 距上次充值22天
            'historical_monthly_recharge_amount': 3676, # 历史月均充值3676元
            'recent_recharge_amount': 0,               # 最近30天充值0元
            
            # 其他风险评分 (模拟)
            'battle_risk': 75,    # 战斗风险高
            'social_risk': 70,    # 社交风险高
            'other_risk': 65      # 其他风险中等
        },
        {
            # 角色二：中等风险用户 - 李四
            'user_id': 10002,
            'character_name': '剑客李四',
            'vip_level': 6,
            'registration_days': 149,  # 注册5个月
            'total_recharge': 8000,
            'total_recharge_count': 12,
            
            # 登录相关数据
            'recent_logins': 16,          # 最近30天登录16次
            'days_since_login': 1,        # 距上次登录1天
            'consecutive_login_days': 8,   # 连续登录8天
            
            # 充值相关数据
            'historical_monthly_recharge_count': 2.4,  # 历史月均充值2.4次
            'recent_recharge_count': 1,                # 最近30天充值1次
            'days_since_recharge': 9,                  # 距上次充值9天
            'historical_monthly_recharge_amount': 1610, # 历史月均充值1610元
            'recent_recharge_amount': 500,             # 最近30天充值500元
            
            # 其他风险评分 (模拟)
            'battle_risk': 35,    # 战斗风险低
            'social_risk': 40,    # 社交风险中等
            'other_risk': 30      # 其他风险低
        },
        {
            # 角色三：低风险稳定用户 - 王五
            'user_id': 10003,
            'character_name': '法师王五',
            'vip_level': 7,
            'registration_days': 187,  # 注册6个月
            'total_recharge': 15000,
            'total_recharge_count': 20,
            
            # 登录相关数据
            'recent_logins': 25,          # 最近30天登录25次
            'days_since_login': 0,        # 距上次登录0天（今天登录）
            'consecutive_login_days': 15,  # 连续登录15天
            
            # 充值相关数据
            'historical_monthly_recharge_count': 3.2,  # 历史月均充值3.2次
            'recent_recharge_count': 3,                # 最近30天充值3次
            'days_since_recharge': 3,                  # 距上次充值3天
            'historical_monthly_recharge_amount': 2408, # 历史月均充值2408元
            'recent_recharge_amount': 1200,            # 最近30天充值1200元
            
            # 其他风险评分 (模拟)
            'battle_risk': 15,    # 战斗风险很低
            'social_risk': 10,    # 社交风险很低
            'other_risk': 20      # 其他风险低
        }
    ]
    
    return users


def format_calculation_details(result: Dict) -> str:
    """格式化计算详情输出"""
    details = f"""
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 用户：{result['character_name']} (ID: {result['user_id']}) - VIP{result['vip_level']}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔢 各维度风险评分：
  • 登录风险: {result['dimension_scores']['login_risk']:.2f}分
  • 充值风险: {result['dimension_scores']['recharge_risk']:.2f}分  
  • 战斗风险: {result['dimension_scores']['battle_risk']:.2f}分
  • 社交风险: {result['dimension_scores']['social_risk']:.2f}分
  • 其他风险: {result['dimension_scores']['other_risk']:.2f}分

⚖️ 权重贡献分析：
  • 登录贡献: {result['weight_contributions']['login_contribution']:.2f}分 (权重25%)
  • 充值贡献: {result['weight_contributions']['recharge_contribution']:.2f}分 (权重30%)
  • 战斗贡献: {result['weight_contributions']['battle_contribution']:.2f}分 (权重10%)
  • 社交贡献: {result['weight_contributions']['social_contribution']:.2f}分 (权重15%)
  • 其他贡献: {result['weight_contributions']['other_contribution']:.2f}分 (权重20%)

🎯 综合风险评估：
  • 综合评分: {result['comprehensive_risk_score']:.2f}分
  • 风险等级: {result['risk_label']}
  • 预测流失日期: {result['predicted_churn_date'] or '无预测'}

🏷️ 风险因素标签："""
    
    if result['risk_factors']:
        for factor in result['risk_factors']:
            primary_text = " (主要因素)" if factor['is_primary'] else ""
            details += f"\n  • {factor['text']}: {factor['score']:.2f}分{primary_text}"
    else:
        details += "\n  • 无显著风险因素"
    
    details += "\n\n💡 建议干预措施："
    for action in result['suggested_actions']:
        priority_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(action['priority'], '⚪')
        details += f"\n  {priority_icon} {action['text']} (优先级: {action['priority']})"
    
    return details


def main():
    """主函数 - 演示风险计算过程"""
    print("🚀 流失预警中心 - 风险计算演示")
    print("=" * 80)
    
    # 初始化计算器
    calculator = ChurnRiskCalculator()
    
    # 创建示例用户数据
    users = create_sample_users()
    
    # 计算结果汇总
    results = []
    
    print("\n📋 开始计算3个用户的流失风险评分...")
    print("-" * 80)
    
    # 计算每个用户的风险评分
    for user_data in users:
        result = calculator.calculate_comprehensive_risk(user_data)
        results.append(result)
        
        # 输出详细计算过程
        print(format_calculation_details(result))
    
    # 输出对比表格
    print("\n" + "=" * 80)
    print("📊 三个用户风险评分对比表")
    print("=" * 80)
    
    # 表格标题
    print(f"{'用户':<12} {'登录风险':<8} {'充值风险':<8} {'战斗风险':<8} {'社交风险':<8} {'其他风险':<8} {'综合评分':<10} {'风险等级':<12}")
    print("-" * 80)
    
    # 表格内容
    for result in results:
        name = result['character_name'][:8]  # 截取前8个字符
        login = result['dimension_scores']['login_risk']
        recharge = result['dimension_scores']['recharge_risk']
        battle = result['dimension_scores']['battle_risk']
        social = result['dimension_scores']['social_risk']
        other = result['dimension_scores']['other_risk']
        total = result['comprehensive_risk_score']
        level = result['risk_label']
        
        print(f"{name:<12} {login:<8.1f} {recharge:<8.1f} {battle:<8.1f} {social:<8.1f} {other:<8.1f} {total:<10.1f} {level:<12}")
    
    # 权重贡献对比
    print("\n📈 权重贡献对比分析:")
    print("-" * 80)
    print(f"{'用户':<12} {'登录贡献':<10} {'充值贡献':<10} {'战斗贡献':<10} {'社交贡献':<10} {'其他贡献':<10} {'总计':<8}")
    print("-" * 80)
    
    for result in results:
        name = result['character_name'][:8]
        contrib = result['weight_contributions']
        total = sum(contrib.values())
        
        print(f"{name:<12} "
              f"{contrib['login_contribution']:<10.1f} "
              f"{contrib['recharge_contribution']:<10.1f} "
              f"{contrib['battle_contribution']:<10.1f} "
              f"{contrib['social_contribution']:<10.1f} "
              f"{contrib['other_contribution']:<10.1f} "
              f"{total:<8.1f}")
    
    # 结论分析
    print("\n🎯 分析结论:")
    print("-" * 40)
    print("• 充值风险是影响用户流失预测的最关键因素 (权重30%)")
    print("• 张三因充值中断22天，充值风险达86.4分，被评为高风险用户")
    print("• 李四虽然充值金额下降，但登录活跃，风险可控")
    print("• 王五各项指标优秀，是稳定的优质用户")
    print("• 建议对不同风险等级用户采取差异化的干预策略")
    
    print(f"\n✅ 计算完成！共分析了 {len(results)} 位用户")
    print("💾 详细的计算过程和算法说明请参考项目文档:")
    print("   docs/流失预警风险计算实例详解.md")


if __name__ == '__main__':
    main()