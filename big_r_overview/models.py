from django.db import models
from django.utils import timezone
from decimal import Decimal


class BigRUser(models.Model):
    """大R用户基础信息模型"""

    # 基础信息
    user_id = models.BigIntegerField(unique=True, verbose_name="用户ID")
    username = models.CharField(max_length=100, verbose_name="用户名")
    character_name = models.CharField(max_length=100, verbose_name="角色名")
    server_id = models.IntegerField(verbose_name="服务器ID")
    server_name = models.CharField(max_length=50, verbose_name="服务器名称")

    # 充值信息
    total_recharge = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="累计充值金额")
    first_recharge_date = models.DateTimeField(null=True, blank=True, verbose_name="首次充值时间")
    last_recharge_date = models.DateTimeField(null=True, blank=True, verbose_name="最后充值时间")

    # 活跃度信息
    last_login_date = models.DateTimeField(null=True, blank=True, verbose_name="最后登录时间")
    total_login_days = models.IntegerField(default=0, verbose_name="累计登录天数")
    consecutive_login_days = models.IntegerField(default=0, verbose_name="连续登录天数")

    # 状态标识
    is_potential = models.BooleanField(default=False, verbose_name="是否为潜力用户")
    is_churn_risk = models.BooleanField(default=False, verbose_name="是否有流失风险")
    churn_risk_level = models.CharField(
        max_length=10,
        choices=[('low', '低风险'), ('medium', '中风险'), ('high', '高风险'), ('critical', '严重风险')],
        default='low',
        verbose_name="流失风险等级"
    )

    # 时间戳
    created_at = models.DateTimeField(verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "大R用户"
        verbose_name_plural = "大R用户"
        indexes = [
            models.Index(fields=['user_id']),
            models.Index(fields=['total_recharge']),
            models.Index(fields=['last_login_date']),
            models.Index(fields=['is_potential']),
            models.Index(fields=['is_churn_risk']),
        ]

    def __str__(self):
        return f"{self.character_name}({self.user_id})"

    @property
    def vip_level_info(self):
        """获取VIP等级信息"""
        try:
            from privileges.models import VIPLevel
            # 根据累计充值金额查找符合条件的最高等级
            vip_level = VIPLevel.objects.filter(
                threshold__lte=self.total_recharge
            ).order_by('-threshold').first()
            return vip_level
        except Exception:
            # 如果privileges应用不存在或查询失败，返回None
            return None
    
    @property  
    def vip_level(self):
        """保持兼容性，返回数字等级"""
        vip_info = self.vip_level_info
        if vip_info:
            return vip_info.level
        else:
            # 后备方案：使用原来的硬编码逻辑
            if self.total_recharge >= 100000:
                return 10
            elif self.total_recharge >= 50000:
                return 9
            elif self.total_recharge >= 20000:
                return 8
            elif self.total_recharge >= 10000:
                return 7
            elif self.total_recharge >= 5000:
                return 6
            elif self.total_recharge >= 2000:
                return 5
            elif self.total_recharge >= 1000:
                return 4
            elif self.total_recharge >= 500:
                return 3
            elif self.total_recharge >= 200:
                return 2
            elif self.total_recharge >= 100:
                return 1
            else:
                return 0

    @property
    def vip_level_name(self):
        """获取VIP等级名称"""
        vip_info = self.vip_level_info
        if vip_info:
            return vip_info.name
        else:
            # 后备方案：使用传统格式
            level = self.vip_level
            return f"VIP{level}" if level > 0 else "普通用户"


class UserRechargeRecord(models.Model):
    """用户充值记录模型"""

    user = models.ForeignKey(BigRUser, on_delete=models.CASCADE, verbose_name="用户")
    order_id = models.CharField(max_length=100, unique=True, verbose_name="订单号")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="充值金额")
    currency = models.CharField(max_length=10, default='CNY', verbose_name="货币类型")
    payment_method = models.CharField(max_length=50, verbose_name="支付方式")

    # 充值状态
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待支付'),
            ('success', '支付成功'),
            ('failed', '支付失败'),
            ('refunded', '已退款')
        ],
        default='pending',
        verbose_name="支付状态"
    )

    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name="支付时间")

    class Meta:
        verbose_name = "用户充值记录"
        verbose_name_plural = "用户充值记录"
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['status']),
            models.Index(fields=['paid_at']),
        ]

    def __str__(self):
        return f"{self.user.character_name} - {self.amount}元"


class UserLoginRecord(models.Model):
    """用户登录记录模型"""

    user = models.ForeignKey(BigRUser, on_delete=models.CASCADE, verbose_name="用户")
    login_date = models.DateField(verbose_name="登录日期")
    login_time = models.DateTimeField(verbose_name="登录时间")
    online_duration = models.IntegerField(default=0, verbose_name="在线时长(分钟)")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="IP地址")
    device_type = models.CharField(max_length=50, null=True, blank=True, verbose_name="设备类型")

    class Meta:
        verbose_name = "用户登录记录"
        verbose_name_plural = "用户登录记录"
        unique_together = ['user', 'login_date']  # 每天只记录一次登录
        indexes = [
            models.Index(fields=['user', 'login_date']),
            models.Index(fields=['login_date']),
        ]

    def __str__(self):
        return f"{self.user.character_name} - {self.login_date}"


class BigROverviewStats(models.Model):
    """大R总览统计数据模型"""

    # 统计日期
    stat_date = models.DateField(unique=True, verbose_name="统计日期")

    # 核心指标
    total_big_r_users = models.IntegerField(default=0, verbose_name="大R用户总数")
    total_potential_users = models.IntegerField(default=0, verbose_name="潜力用户数量")
    total_churn_warning_users = models.IntegerField(default=0, verbose_name="流失预警用户数")

    # ARPU相关
    total_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="总收入")
    avg_arpu = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="平均ARPU值")

    # VIP分布统计
    vip_distribution = models.JSONField(default=dict, verbose_name="VIP等级分布")

    # 增长数据
    new_big_r_users = models.IntegerField(default=0, verbose_name="新增大R用户")
    lost_big_r_users = models.IntegerField(default=0, verbose_name="流失大R用户")

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "大R总览统计"
        verbose_name_plural = "大R总览统计"
        ordering = ['-stat_date']

    def __str__(self):
        return f"{self.stat_date} - 用户总数: {self.total_big_r_users}"


class UserBehaviorAnalysis(models.Model):
    """用户行为分析模型"""

    user = models.OneToOneField(BigRUser, on_delete=models.CASCADE, verbose_name="用户")

    # 充值行为分析
    avg_recharge_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="平均充值金额")
    recharge_frequency = models.FloatField(default=0, verbose_name="充值频率(次/月)")
    days_since_last_recharge = models.IntegerField(default=0, verbose_name="距离上次充值天数")

    # 活跃度分析
    avg_online_duration = models.IntegerField(default=0, verbose_name="平均在线时长(分钟/天)")
    login_frequency = models.FloatField(default=0, verbose_name="登录频率(天/周)")
    days_since_last_login = models.IntegerField(default=0, verbose_name="距离上次登录天数")

    # 潜力评分
    potential_score = models.FloatField(default=0, verbose_name="潜力评分(0-100)")
    churn_risk_score = models.FloatField(default=0, verbose_name="流失风险评分(0-100)")

    # 更新时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "用户行为分析"
        verbose_name_plural = "用户行为分析"

    def __str__(self):
        return f"{self.user.character_name} - 潜力评分: {self.potential_score}"
