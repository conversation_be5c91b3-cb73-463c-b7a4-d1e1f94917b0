from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal
import random

from churn_warning.models import (
    ChurnRiskFactor, ChurnPrediction, ChurnWarningStats,
    ChurnWarningConfig
)
from big_r_overview.models import BigRUser
from churn_warning.services import ChurnPredictionService


class Command(BaseCommand):
    help = '初始化流失预警中心测试数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有数据'
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write(self.style.WARNING('清除现有流失预警数据...'))
            self.clear_existing_data()

        self.stdout.write(self.style.SUCCESS('开始初始化流失预警数据...'))

        # 1. 创建风险因子配置
        self.create_risk_factors()

        # 2. 创建系统配置
        self.create_warning_config()

        # 3. 确保有足够的大R用户数据
        self.ensure_bigr_users()

        # 4. 生成流失预测数据
        self.generate_churn_predictions()

        # 5. 生成统计数据
        self.generate_warning_stats()

        self.stdout.write(self.style.SUCCESS('流失预警数据初始化完成!'))

    def clear_existing_data(self):
        """清除现有数据"""
        ChurnPrediction.objects.all().delete()
        ChurnWarningStats.objects.all().delete()
        ChurnRiskFactor.objects.all().delete()
        ChurnWarningConfig.objects.all().delete()

    def create_risk_factors(self):
        """创建风险因子配置"""
        self.stdout.write('创建风险因子配置...')

        factors = [
            {
                'factor_type': 'login_frequency',
                'description': '用户登录频率异常，可能表示活跃度下降',
                'weight': 0.30,
                'threshold_value': 10.0,  # 30天内登录次数低于10次
            },
            {
                'factor_type': 'recharge_frequency',
                'description': '充值频率下降，可能失去付费意愿',
                'weight': 0.40,
                'threshold_value': 2.0,  # 月充值次数低于2次
            },
            {
                'factor_type': 'activity_risk',
                'description': '在线时长和任务完成度双重下降，用户参与度降低',
                'weight': 0.30,
                'threshold_value': 50.0,  # 活跃度下降超过50%
            },
        ]

        for factor_data in factors:
            factor, created = ChurnRiskFactor.objects.get_or_create(
                factor_type=factor_data['factor_type'],
                defaults=factor_data
            )
            if created:
                self.stdout.write(f'  * 创建风险因子: {factor.get_factor_type_display()}')

    def create_warning_config(self):
        """创建系统配置"""
        self.stdout.write('创建系统配置...')

        config = ChurnWarningConfig.get_config()
        config.critical_threshold = 80.0
        config.high_threshold = 60.0
        config.medium_threshold = 40.0
        config.prediction_days = 30
        config.min_confidence_score = 0.7
        config.auto_contact_critical = True
        config.auto_gift_threshold = 85.0
        config.stats_retention_days = 90
        config.save()

        self.stdout.write('  * 系统配置创建完成')

    def ensure_bigr_users(self):
        """确保有足够的大R用户数据"""
        existing_count = BigRUser.objects.count()
        if existing_count < 50:
            self.stdout.write(f'当前只有 {existing_count} 位用户，创建更多测试用户...')
            
            # 创建一些测试用户
            for i in range(50 - existing_count):
                user_id = 20000 + i
                BigRUser.objects.get_or_create(
                    user_id=user_id,
                    defaults={
                        'username': f'test_user_{user_id}',
                        'character_name': f'测试角色{user_id}',
                        'server_id': random.randint(1, 5),
                        'server_name': f'服务器{random.randint(1, 5)}',
                        'total_recharge': Decimal(str(random.uniform(1000, 50000))),
                        'first_recharge_date': timezone.now() - timedelta(days=random.randint(30, 365)),
                        'last_recharge_date': timezone.now() - timedelta(days=random.randint(1, 30)),
                        'last_login_date': timezone.now() - timedelta(days=random.randint(0, 10)),
                        'total_login_days': random.randint(50, 300),
                        'consecutive_login_days': random.randint(0, 30),
                        'created_at': timezone.now() - timedelta(days=random.randint(30, 365)),
                    }
                )
            self.stdout.write(f'  * 创建了 {50 - existing_count} 位测试用户')

    def generate_churn_predictions(self):
        """生成流失预测数据"""
        self.stdout.write('生成流失预测数据...')

        users = BigRUser.objects.all()
        created_count = 0

        for user in users:
            if not ChurnPrediction.objects.filter(user=user).exists():
                # 随机生成风险等级和评分
                risk_levels = ['low', 'medium', 'high', 'critical']
                weights = [0.4, 0.35, 0.2, 0.05]  # 权重：低风险用户较多
                risk_level = random.choices(risk_levels, weights=weights)[0]

                # 根据风险等级确定评分范围
                if risk_level == 'critical':
                    risk_score = random.uniform(80, 95)
                elif risk_level == 'high':
                    risk_score = random.uniform(60, 79)
                elif risk_level == 'medium':
                    risk_score = random.uniform(40, 59)
                else:
                    risk_score = random.uniform(10, 39)

                # 生成各维度评分
                login_score = random.uniform(0, 100)
                recharge_score = random.uniform(0, 100)
                activity_score = random.uniform(0, 100)

                # 生成风险因素
                risk_factors = []
                factor_types = [
                    {'type': 'login', 'text': '登录风险', 'score': login_score},
                    {'type': 'recharge', 'text': '充值风险', 'score': recharge_score},
                    {'type': 'activity', 'text': '活跃风险', 'score': activity_score}
                ]
                
                for factor in factor_types:
                    if factor['score'] >= 60:  # 只添加风险评分较高的因素
                        risk_factors.append({
                            'type': factor['type'],
                            'text': factor['text'],
                            'score': factor['score'],
                            'is_primary': factor['score'] >= 80
                        })

                # 生成建议措施
                suggested_actions = []
                if risk_level in ['critical', 'high']:
                    suggested_actions.extend([
                        {'type': 'contact', 'text': '立即联系用户', 'priority': 'high'},
                        {'type': 'gift', 'text': '赠送高价值礼包', 'priority': 'high'}
                    ])
                elif risk_level == 'medium':
                    suggested_actions.append(
                        {'type': 'discount', 'text': '提供充值折扣', 'priority': 'medium'}
                    )

                # 创建预测记录
                prediction = ChurnPrediction.objects.create(
                    user=user,
                    risk_level=risk_level,
                    risk_score=risk_score,
                    login_risk_score=login_score,
                    recharge_risk_score=recharge_score,
                    activity_risk_score=activity_score,
                    predicted_churn_date=date.today() + timedelta(days=random.randint(7, 60)) if risk_level != 'low' else None,
                    confidence_score=random.uniform(0.7, 0.95),
                    risk_factors=risk_factors,
                    suggested_actions=suggested_actions,
                    status=random.choice(['pending', 'contacted', 'intervened'])
                )
                created_count += 1

        self.stdout.write(f'  * 创建了 {created_count} 条流失预测记录')

    def generate_warning_stats(self):
        """生成统计数据"""
        self.stdout.write('生成统计数据...')

        # 生成最近7天的统计数据
        for i in range(7):
            stat_date = date.today() - timedelta(days=i)
            
            if not ChurnWarningStats.objects.filter(stat_date=stat_date).exists():
                # 计算当日各风险等级用户数
                predictions = ChurnPrediction.objects.filter(
                    updated_at__date=stat_date
                )
                
                critical_users = predictions.filter(risk_level='critical').count()
                high_users = predictions.filter(risk_level='high').count()
                medium_users = predictions.filter(risk_level='medium').count()
                low_users = predictions.filter(risk_level='low').count()

                # 如果当天没有数据，使用随机数据
                if not predictions.exists():
                    critical_users = random.randint(8, 15)
                    high_users = random.randint(20, 35)
                    medium_users = random.randint(50, 80)
                    low_users = random.randint(40, 60)

                # 计算变化（相对于前一天）
                if i < 6:  # 不是最早的一天
                    prev_stats = ChurnWarningStats.objects.filter(
                        stat_date=stat_date + timedelta(days=1)
                    ).first()
                    
                    if prev_stats:
                        critical_change = critical_users - prev_stats.critical_users
                        high_change = high_users - prev_stats.high_risk_users
                        medium_change = medium_users - prev_stats.medium_risk_users
                        low_change = low_users - prev_stats.low_risk_users
                    else:
                        critical_change = high_change = medium_change = low_change = 0
                else:
                    critical_change = high_change = medium_change = low_change = 0

                # 创建统计记录
                ChurnWarningStats.objects.create(
                    stat_date=stat_date,
                    critical_users=critical_users,
                    high_risk_users=high_users,
                    medium_risk_users=medium_users,
                    low_risk_users=low_users,
                    contacted_users=random.randint(5, 15),
                    intervened_users=random.randint(3, 10),
                    recovered_users=random.randint(1, 5),
                    churned_users=random.randint(0, 3),
                    avg_risk_score=random.uniform(35, 55),
                    critical_change=critical_change,
                    high_risk_change=high_change,
                    medium_risk_change=medium_change,
                    low_risk_change=low_change,
                    intervention_success_rate=random.uniform(60, 85)
                )

        self.stdout.write('  * 生成了7天的统计数据')