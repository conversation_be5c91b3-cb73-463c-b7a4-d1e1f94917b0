{% extends 'base.html' %}
{% load static %}

{% block title %}特权管理 - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}特权管理{% endblock %}

{% block extra_css %}
<style>
/* 苹果风格特权管理页面样式 */
.privilege-container {
    padding: 20px;
    min-height: 100vh;
    margin: 0;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.9) 100%);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.privilege-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.privilege-container > * {
    position: relative;
    z-index: 1;
}

/* 页面头部 */
.page-header {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 20px 40px -12px rgba(0, 0, 0, 0.08),
        0 8px 16px -8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-header:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 12px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        rgba(139, 92, 246, 0.8) 0%, 
        rgba(16, 185, 129, 0.8) 50%, 
        rgba(245, 158, 11, 0.8) 100%);
    border-radius: 16px 16px 0 0;
}

.page-title {
    font-size: 1.6rem;
    font-weight: 800;
    margin-bottom: 8px;
    background: linear-gradient(135deg, 
        rgba(139, 92, 246, 0.9) 0%, 
        rgba(16, 185, 129, 0.9) 50%,
        rgba(245, 158, 11, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.page-description {
    color: rgba(100, 116, 139, 0.8);
    margin: 0;
    font-size: 0.95rem;
    font-weight: 500;
    line-height: 1.5;
}

/* 特权图标渐变色 */
.privilege-icon.gift {
    background: linear-gradient(135deg, #10b981, #059669);
}

.privilege-icon.percent {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.privilege-icon.speed {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.privilege-icon.star {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.privilege-icon.shield {
    background: linear-gradient(135deg, var(--primary), #4f46e5);
}

/* 统计卡片网格 - 强制一行显示 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 8px 24px -4px rgba(0, 0, 0, 0.08),
        0 4px 12px -4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: 16px 16px 0 0;
    background: linear-gradient(90deg, 
        rgba(139, 92, 246, 0.8) 0%, 
        rgba(124, 58, 237, 0.9) 100%);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.stat-card.stat-success::before {
    background: linear-gradient(90deg, 
        rgba(16, 185, 129, 0.8) 0%, 
        rgba(5, 150, 105, 0.9) 100%);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.stat-card.stat-error::before {
    background: linear-gradient(90deg, 
        rgba(239, 68, 68, 0.8) 0%, 
        rgba(220, 38, 38, 0.9) 100%);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.stat-card.stat-info::before {
    background: linear-gradient(90deg, 
        rgba(6, 182, 212, 0.8) 0%, 
        rgba(8, 145, 178, 0.9) 100%);
    box-shadow: 0 2px 8px rgba(6, 182, 212, 0.3);
}

.stat-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 
        0 20px 40px -8px rgba(0, 0, 0, 0.15),
        0 8px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;
}

.stat-title {
    font-size: 0.8rem;
    color: rgba(100, 116, 139, 0.8);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.6px;
}

.stat-icon {
    width: 44px;
    height: 44px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.4rem;
    box-shadow: 
        0 8px 16px -4px rgba(0, 0, 0, 0.15),
        0 4px 8px -4px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, 
        rgba(139, 92, 246, 0.9) 0%, 
        rgba(124, 58, 237, 1) 100%);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 
        0 12px 24px -4px rgba(0, 0, 0, 0.2),
        0 8px 16px -4px rgba(0, 0, 0, 0.12);
}

.stat-value {
    font-size: 2.2rem;
    font-weight: 800;
    color: rgba(15, 23, 42, 0.9);
    margin-bottom: 12px;
    line-height: 1;
    letter-spacing: -0.02em;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-change.positive {
    color: rgba(16, 185, 129, 0.9);
    background: rgba(236, 253, 245, 0.8);
}

.stat-change.negative {
    color: rgba(239, 68, 68, 0.9);
    background: rgba(254, 242, 242, 0.8);
}

/* 工具栏 */
.toolbar {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 8px 24px -4px rgba(0, 0, 0, 0.08),
        0 4px 12px -4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toolbar-search {
    flex: 1;
    min-width: 300px;
    position: relative;
}

.toolbar-search::before {
    content: '\F52A';
    font-family: 'bootstrap-icons';
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(100, 116, 139, 0.6);
    font-size: 1rem;
    z-index: 1;
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 36px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.search-input:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.12),
        0 4px 8px -2px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.toolbar-filters {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-select {
    padding: 10px 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 0.85rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-weight: 500;
    cursor: pointer;
    min-width: 120px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.filter-select:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.12),
        0 4px 8px -2px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.toolbar-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 按钮样式 */
.action-btn {
    padding: 10px 16px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 6px;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px -2px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-btn.primary {
    background: linear-gradient(135deg, 
        rgba(139, 92, 246, 0.9) 0%, 
        rgba(124, 58, 237, 1) 100%);
    color: white;
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow: 
        0 4px 8px -2px rgba(139, 92, 246, 0.3),
        0 2px 4px -2px rgba(139, 92, 246, 0.2);
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, 
        rgba(124, 58, 237, 0.9) 0%, 
        rgba(109, 40, 217, 1) 100%);
    box-shadow: 
        0 8px 16px -4px rgba(139, 92, 246, 0.4),
        0 4px 8px -4px rgba(139, 92, 246, 0.3);
}

.action-btn.secondary {
    background: linear-gradient(135deg, 
        rgba(16, 185, 129, 0.9) 0%, 
        rgba(5, 150, 105, 1) 100%);
    color: white;
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 
        0 4px 8px -2px rgba(16, 185, 129, 0.3),
        0 2px 4px -2px rgba(16, 185, 129, 0.2);
}

.action-btn.secondary:hover {
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.9) 0%, 
        rgba(4, 120, 87, 1) 100%);
    box-shadow: 
        0 8px 16px -4px rgba(16, 185, 129, 0.4),
        0 4px 8px -4px rgba(16, 185, 129, 0.3);
}

.action-btn.outline {
    background: rgba(255, 255, 255, 0.6);
    color: rgba(100, 116, 139, 0.9);
    border-color: rgba(100, 116, 139, 0.3);
}

.action-btn.outline:hover {
    background: rgba(100, 116, 139, 0.1);
    color: rgba(71, 85, 105, 0.9);
    border-color: rgba(100, 116, 139, 0.5);
}

.action-btn.error {
    background: linear-gradient(135deg, 
        rgba(239, 68, 68, 0.9) 0%, 
        rgba(220, 38, 38, 1) 100%);
    color: white;
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 
        0 4px 8px -2px rgba(239, 68, 68, 0.3),
        0 2px 4px -2px rgba(239, 68, 68, 0.2);
}

.action-btn.error:hover {
    background: linear-gradient(135deg, 
        rgba(220, 38, 38, 0.9) 0%, 
        rgba(185, 28, 28, 1) 100%);
    box-shadow: 
        0 8px 16px -4px rgba(239, 68, 68, 0.4),
        0 4px 8px -4px rgba(239, 68, 68, 0.3);
}

/* 特权卡片网格 - 一行显示4个 */
.privileges-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 24px;
}

/* 特权卡片样式 */
.privilege-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 
        0 8px 24px -4px rgba(0, 0, 0, 0.08),
        0 4px 12px -4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.privilege-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: 16px 16px 0 0;
    background: linear-gradient(90deg, 
        rgba(139, 92, 246, 0.8) 0%, 
        rgba(16, 185, 129, 0.8) 50%, 
        rgba(245, 158, 11, 0.8) 100%);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.privilege-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 
        0 20px 40px -8px rgba(0, 0, 0, 0.15),
        0 8px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.privilege-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 16px;
}

.privilege-info {
    flex: 1;
}

.privilege-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: rgba(15, 23, 42, 0.9);
    margin-bottom: 4px;
    letter-spacing: -0.02em;
    line-height: 1.3;
}

.privilege-category {
    font-size: 0.8rem;
    color: rgba(100, 116, 139, 0.8);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.privilege-icon {
    width: 48px;
    height: 48px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 
        0 8px 16px -4px rgba(0, 0, 0, 0.15),
        0 4px 8px -4px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.privilege-card:hover .privilege-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 
        0 12px 24px -4px rgba(0, 0, 0, 0.2),
        0 8px 16px -4px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.privilege-description {
    color: rgba(71, 85, 105, 0.8);
    font-size: 0.85rem;
    line-height: 1.5;
    margin-bottom: 16px;
    background: rgba(248, 250, 252, 0.6);
    padding: 12px;
    border-radius: 10px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    backdrop-filter: blur(5px);
}

.privilege-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(226, 232, 240, 0.3);
}

.privilege-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.privilege-status.active {
    color: rgba(16, 185, 129, 0.9);
    background: rgba(236, 253, 245, 0.8);
    border-color: rgba(209, 250, 229, 0.5);
}

.privilege-status.disabled {
    color: rgba(100, 116, 139, 0.8);
    background: rgba(248, 250, 252, 0.8);
    border-color: rgba(226, 232, 240, 0.5);
}

.privilege-status.maintenance {
    color: rgba(245, 158, 11, 0.9);
    background: rgba(254, 243, 199, 0.8);
    border-color: rgba(253, 230, 138, 0.5);
}

.privilege-actions {
    display: flex;
    gap: 8px;
}

.card-action-btn {
    padding: 6px 10px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-size: 0.7rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 4px;
}

.card-action-btn:hover {
    background: rgba(248, 250, 252, 0.9);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px -2px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-action-btn.primary {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.9) 0%, 
        rgba(37, 99, 235, 1) 100%);
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 
        0 2px 4px -1px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.card-action-btn.primary:hover {
    background: linear-gradient(135deg, 
        rgba(37, 99, 235, 0.9) 0%, 
        rgba(29, 78, 216, 1) 100%);
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.4),
        0 2px 4px -2px rgba(59, 130, 246, 0.3);
}

.card-action-btn.danger {
    background: linear-gradient(135deg, 
        rgba(239, 68, 68, 0.9) 0%, 
        rgba(220, 38, 38, 1) 100%);
    color: white;
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 
        0 2px 4px -1px rgba(239, 68, 68, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.card-action-btn.danger:hover {
    background: linear-gradient(135deg, 
        rgba(220, 38, 38, 0.9) 0%, 
        rgba(185, 28, 28, 1) 100%);
    box-shadow: 
        0 4px 8px -2px rgba(239, 68, 68, 0.4),
        0 2px 4px -2px rgba(239, 68, 68, 0.3);
}

/* 搜索框动画 */
.search-input:focus {
    transform: scale(1.01);
}

/* 按钮光泽效果 */
.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* 加载状态样式 */
.loading-container {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    border-radius: 12px;
    margin: 20px;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    color: rgba(100, 116, 139, 0.8);
    font-weight: 500;
}

.loading-spinner i {
    font-size: 2.5rem;
    animation: spin 1s linear infinite;
    color: rgba(139, 92, 246, 0.8);
    background: rgba(248, 250, 252, 0.8);
    padding: 16px;
    border-radius: 50%;
    box-shadow: 
        0 4px 8px -2px rgba(139, 92, 246, 0.2),
        0 2px 4px -2px rgba(139, 92, 246, 0.1);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px;
    text-align: center;
    color: rgba(100, 116, 139, 0.8);
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    border-radius: 12px;
    margin: 20px;
}

.empty-icon {
    font-size: 3.5rem;
    margin-bottom: 16px;
    opacity: 0.4;
    color: rgba(139, 92, 246, 0.6);
}

.empty-state h3 {
    margin-bottom: 8px;
    color: rgba(15, 23, 42, 0.9);
    font-weight: 700;
    font-size: 1.2rem;
}

.empty-state p {
    margin-bottom: 24px;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .privileges-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .privileges-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
}

@media (max-width: 768px) {
    .privilege-container {
        padding: 16px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .stat-card {
        padding: 16px;
    }

    .stat-value {
        font-size: 1.8rem;
    }

    .stat-icon {
        width: 36px;
        height: 36px;
        font-size: 1.2rem;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 16px;
    }

    .toolbar-search {
        min-width: auto;
        width: 100%;
    }

    .toolbar-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .privileges-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-container {
        width: 95%;
        margin: 16px;
    }
    
    .modal-header,
    .modal-body,
    .modal-form,
    .modal-footer {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 8px 16px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

.modal-sm {
    max-width: 400px;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
}

.modal-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: rgba(15, 23, 42, 0.9);
    margin: 0;
    letter-spacing: -0.02em;
}

.modal-close {
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.6);
    color: rgba(100, 116, 139, 0.8);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(239, 68, 68, 0.1);
    color: rgba(239, 68, 68, 0.9);
    border-color: rgba(239, 68, 68, 0.3);
    transform: scale(1.05);
}

.modal-body {
    padding: 20px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
}

.modal-form {
    padding: 20px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 0 0 16px 16px;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.8rem;
    font-weight: 600;
    color: rgba(15, 23, 42, 0.9);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 10px;
    font-size: 0.85rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    color: rgba(15, 23, 42, 0.9);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: rgba(139, 92, 246, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 
        0 0 0 3px rgba(139, 92, 246, 0.12),
        0 4px 8px -2px rgba(139, 92, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-help {
    font-size: 0.75rem;
    color: rgba(100, 116, 139, 0.8);
    margin-top: 6px;
    font-weight: 500;
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    margin-top: 24px;
    font-weight: 500;
    color: rgba(15, 23, 42, 0.9);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 6px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: linear-gradient(135deg, 
        rgba(139, 92, 246, 0.9) 0%, 
        rgba(124, 58, 237, 1) 100%);
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 
        0 4px 8px -2px rgba(139, 92, 246, 0.3),
        0 2px 4px -2px rgba(139, 92, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 14px;
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 删除警告样式 */
.delete-warning {
    text-align: center;
    padding: 24px;
    background: rgba(254, 242, 242, 0.6);
    backdrop-filter: blur(8px);
    border-radius: 12px;
    border: 1px solid rgba(254, 226, 226, 0.5);
}

.delete-warning i {
    font-size: 3.5rem;
    color: rgba(245, 158, 11, 0.8);
    margin-bottom: 16px;
    background: rgba(254, 243, 199, 0.8);
    padding: 16px;
    border-radius: 50%;
    box-shadow: 
        0 4px 8px -2px rgba(245, 158, 11, 0.2),
        0 2px 4px -2px rgba(245, 158, 11, 0.1);
}

.delete-warning p {
    margin-bottom: 8px;
    color: rgba(15, 23, 42, 0.9);
    font-weight: 600;
    font-size: 1rem;
}

.warning-text {
    font-size: 0.85rem;
    color: rgba(100, 116, 139, 0.8);
    line-height: 1.5;
    font-weight: 500;
}

/* 按钮加载状态 */
.btn-loading .spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 响应式 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-container {
        width: 95%;
        margin: var(--spacing-md);
    }
}
</style>
{% endblock %}

{% block content %}
<!-- 特权管理容器 -->
<div class="privilege-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">{{ page_title }}</h1>
        <p class="page-description">{{ page_description }}</p>
    </div>

    <!-- 统计概览 - 一行显示 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-title">特权总数</div>
                <div class="stat-icon">
                    <i class="bi bi-award"></i>
                </div>
            </div>
            <div class="stat-value" id="totalPrivileges">{{ stats.total_privileges }}</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                <span>系统特权</span>
            </div>
        </div>

        <div class="stat-card stat-success">
            <div class="stat-header">
                <div class="stat-title">已启用特权</div>
                <div class="stat-icon" style="background: var(--success);">
                    <i class="bi bi-check-circle"></i>
                </div>
            </div>
            <div class="stat-value" id="activePrivileges">{{ stats.active_privileges }}</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                <span id="activeRate">{% if stats.total_privileges > 0 %}{{ stats.active_privileges|floatformat:0 }}/{{ stats.total_privileges }} 启用{% else %}0% 启用率{% endif %}</span>
            </div>
        </div>

        <div class="stat-card stat-error">
            <div class="stat-header">
                <div class="stat-title">已禁用特权</div>
                <div class="stat-icon" style="background: var(--error);">
                    <i class="bi bi-x-circle"></i>
                </div>
            </div>
            <div class="stat-value" id="disabledPrivileges">{{ stats.disabled_privileges }}</div>
            <div class="stat-change negative">
                <i class="bi bi-arrow-down"></i>
                <span id="disabledRate">{% if stats.total_privileges > 0 %}{{ stats.disabled_privileges|floatformat:0 }}/{{ stats.total_privileges }} 禁用{% else %}0% 禁用率{% endif %}</span>
            </div>
        </div>

        <div class="stat-card stat-info">
            <div class="stat-header">
                <div class="stat-title">活跃使用用户</div>
                <div class="stat-icon" style="background: var(--info);">
                    <i class="bi bi-people"></i>
                </div>
            </div>
            <div class="stat-value" id="totalActiveUsers">{{ stats.total_active_users|floatformat:0 }}</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                <span>总活跃用户</span>
            </div>
        </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
        <div class="toolbar-search">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索特权名称、类型或描述...">
        </div>
        
        <div class="toolbar-filters">
            <select class="filter-select">
                <option value="">所有类型</option>
                <option value="gift">礼品类</option>
                <option value="discount">折扣类</option>
                <option value="privilege">特权类</option>
            </select>
            
            <select class="filter-select">
                <option value="">所有状态</option>
                <option value="active">已启用</option>
                <option value="disabled">已禁用</option>
                <option value="maintenance">维护中</option>
            </select>
        </div>
        
        <div class="toolbar-actions">
            <button class="action-btn primary">
                <i class="bi bi-plus"></i>
                添加特权
            </button>
            <button class="action-btn secondary">
                <i class="bi bi-upload"></i>
                批量导入
            </button>
            <button class="action-btn outline">
                <i class="bi bi-download"></i>
                导出配置
            </button>
        </div>
    </div>

    <!-- 特权卡片网格 -->
    <div class="privileges-grid" id="privilegesGrid">
        <!-- 加载中状态 -->
        <div class="loading-container" id="loadingContainer">
        <div class="loading-spinner">
            <i class="bi bi-arrow-clockwise"></i>
            <span>正在加载特权数据...</span>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
        <div class="empty-icon">
            <i class="bi bi-inbox"></i>
        </div>
        <h3>暂无特权数据</h3>
        <p>点击"添加特权"按钮创建第一个特权</p>
        <button class="action-btn primary" onclick="showAddModal()">
            <i class="bi bi-plus"></i>
            添加特权
        </button>
        </div>

        <!-- 示例特权卡片 -->
        <div class="privilege-card">
            <div class="privilege-header">
                <div class="privilege-info">
                    <div class="privilege-name">每日登录礼包</div>
                    <div class="privilege-category">礼品类</div>
                </div>
                <div class="privilege-icon privilege-icon-gift">
                    <i class="bi bi-gift"></i>
                </div>
            </div>
            <div class="privilege-description">
                VIP用户每日首次登录即可获得专属礼包奖励，包含游戏币、道具等丰富内容。
            </div>
            <div class="privilege-footer">
                <div class="privilege-status active">
                    <i class="bi bi-check-circle"></i>
                    已启用
                </div>
                <div class="privilege-actions">
                    <button class="card-action-btn primary">
                        <i class="bi bi-pencil"></i>
                        编辑
                    </button>
                    <button class="card-action-btn">
                        <i class="bi bi-eye"></i>
                        查看
                    </button>
                </div>
            </div>
        </div>

        <div class="privilege-card">
            <div class="privilege-header">
                <div class="privilege-info">
                    <div class="privilege-name">商城优惠折扣</div>
                    <div class="privilege-category">折扣类</div>
                </div>
                <div class="privilege-icon privilege-icon-percent">
                    <i class="bi bi-percent"></i>
                </div>
            </div>
            <div class="privilege-description">
                VIP用户在游戏商城购买道具时享受专属折扣优惠，折扣力度根据VIP等级递增。
            </div>
            <div class="privilege-footer">
                <div class="privilege-status active">
                    <i class="bi bi-check-circle"></i>
                    已启用
                </div>
                <div class="privilege-actions">
                    <button class="card-action-btn primary">
                        <i class="bi bi-pencil"></i>
                        编辑
                    </button>
                    <button class="card-action-btn">
                        <i class="bi bi-eye"></i>
                        查看
                    </button>
                </div>
            </div>
        </div>

        <div class="privilege-card">
            <div class="privilege-header">
                <div class="privilege-info">
                    <div class="privilege-name">经验加速BUFF</div>
                    <div class="privilege-category">特权类</div>
                </div>
                <div class="privilege-icon privilege-icon-speed">
                    <i class="bi bi-lightning"></i>
                </div>
            </div>
            <div class="privilege-description">
                VIP用户获得经验值时享受额外加成，提升角色升级速度，让游戏体验更加流畅。
            </div>
            <div class="privilege-footer">
                <div class="privilege-status maintenance">
                    <i class="bi bi-tools"></i>
                    维护中
                </div>
                <div class="privilege-actions">
                    <button class="card-action-btn primary">
                        <i class="bi bi-pencil"></i>
                        编辑
                    </button>
                    <button class="card-action-btn danger">
                        <i class="bi bi-trash"></i>
                        删除
                    </button>
                </div>
            </div>
        </div>

        <div class="privilege-card">
            <div class="privilege-header">
                <div class="privilege-info">
                    <div class="privilege-name">专属客服通道</div>
                    <div class="privilege-category">服务类</div>
                </div>
                <div class="privilege-icon privilege-icon-star">
                    <i class="bi bi-star"></i>
                </div>
            </div>
            <div class="privilege-description">
                VIP用户享受专属客服通道，优先处理问题咨询，提供一对一专业服务支持。
            </div>
            <div class="privilege-footer">
                <div class="privilege-status disabled">
                    <i class="bi bi-x-circle"></i>
                    已禁用
                </div>
                <div class="privilege-actions">
                    <button class="card-action-btn primary">
                        <i class="bi bi-pencil"></i>
                        编辑
                    </button>
                    <button class="card-action-btn">
                        <i class="bi bi-eye"></i>
                        查看
                    </button>
                </div>
            </div>
        </div>

        <!-- 动态加载的特权卡片将在这里显示 -->
    </div>

<!-- 添加/编辑特权模态框 -->
<div class="modal-overlay" id="privilegeModal" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h2 class="modal-title" id="modalTitle">添加特权</h2>
            <button class="modal-close" id="modalClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <form class="modal-form" id="privilegeForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="privilegeName">特权名称 *</label>
                    <input type="text" id="privilegeName" name="name" required maxlength="100" placeholder="请输入特权名称">
                </div>

                <div class="form-group">
                    <label for="privilegeCategory">特权分类 *</label>
                    <select id="privilegeCategory" name="category_id" required>
                        <option value="">请选择分类</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="privilegeVipLevel">最低VIP等级 *</label>
                    <select id="privilegeVipLevel" name="min_vip_level_id" required>
                        <option value="">请选择VIP等级</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="privilegeStatus">状态</label>
                    <select id="privilegeStatus" name="status">
                        <option value="active">已启用</option>
                        <option value="disabled">已禁用</option>
                        <option value="maintenance">维护中</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="privilegeDescription">特权描述 *</label>
                <textarea id="privilegeDescription" name="description" required rows="4" placeholder="请输入特权描述"></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="privilegeSortOrder">排序</label>
                    <input type="number" id="privilegeSortOrder" name="sort_order" value="0" min="0">
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="privilegeFeatured" name="is_featured">
                        <span class="checkbox-custom"></span>
                        推荐特权
                    </label>
                </div>
            </div>


        </form>

        <div class="modal-footer">
            <button type="button" class="action-btn secondary" id="modalCancel">取消</button>
            <button type="submit" class="action-btn primary" id="modalSubmit" form="privilegeForm">
                <span class="btn-text">保存</span>
                <span class="btn-loading" style="display: none;">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    保存中...
                </span>
            </button>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal-overlay" id="deleteModal" style="display: none;">
    <div class="modal-container modal-sm">
        <div class="modal-header">
            <h2 class="modal-title">确认删除</h2>
            <button class="modal-close" id="deleteModalClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <div class="modal-body">
            <div class="delete-warning">
                <i class="bi bi-exclamation-triangle"></i>
                <p>确定要删除特权 "<span id="deletePrivilegeName"></span>" 吗？</p>
                <p class="warning-text">此操作不可撤销，相关的统计数据也将被删除。</p>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="action-btn secondary" id="deleteCancel">取消</button>
            <button type="button" class="action-btn error" id="deleteConfirm">
                <span class="btn-text">确认删除</span>
                <span class="btn-loading" style="display: none;">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    删除中...
                </span>
            </button>
        </div>
    </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/privilege-management.js' %}"></script>
{% endblock %}
