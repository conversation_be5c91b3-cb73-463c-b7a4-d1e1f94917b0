from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Count, Q
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import datetime, timedelta
import json

from .models import PrivilegeProcessRecord
from privileges.models import Privilege


def privilege_process_list(request):
    """特权处理列表页面视图"""
    context = {
        'page_title': '特权处理列表',
        'page_description': '查看所有已处理的特权记录',
    }
    return render(request, 'service_analysis/privilege_process_list.html', context)


def privilege_process_list_api(request):
    """特权处理列表API"""
    # 获取查询参数
    search = request.GET.get('search', '').strip()
    privilege_type = request.GET.get('privilege_type', '')
    vip_level = request.GET.get('vip_level', '')
    processor = request.GET.get('processor', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 20))

    # 构建查询
    queryset = PrivilegeProcessRecord.objects.select_related('privilege')

    if search:
        queryset = queryset.filter(
            Q(character_name__icontains=search) |
            Q(character_id__icontains=search) |
            Q(process_detail__icontains=search)
        )

    if privilege_type:
        queryset = queryset.filter(privilege__id=privilege_type)

    if vip_level:
        queryset = queryset.filter(vip_level=vip_level)

    if processor:
        queryset = queryset.filter(processor__icontains=processor)

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            queryset = queryset.filter(processed_at__date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            queryset = queryset.filter(processed_at__date__lte=date_to_obj)
        except ValueError:
            pass

    # 分页
    paginator = Paginator(queryset, page_size)
    page_obj = paginator.get_page(page)

    # 序列化数据
    records_data = []
    for record in page_obj:
        records_data.append({
            'id': record.id,
            'character_name': record.character_name,
            'character_id': record.character_id,
            'vip_level': record.vip_level,
            'vip_level_name': record.vip_level_name,
            'privilege': {
                'id': record.privilege.id,
                'name': record.privilege.name,
                'description': record.privilege.description,
                'category': record.privilege.category.display_name,
                'icon': record.privilege.category.icon,
            },
            'process_detail': record.process_detail,
            'processor': record.processor,
            'processed_at': record.processed_at.isoformat(),
        })

    return JsonResponse({
        'success': True,
        'data': {
            'records': records_data,
            'pagination': {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }
        }
    })


def privilege_filter_options_api(request):
    """获取筛选选项API"""
    try:
        # 获取所有特权类型
        privileges = []
        for privilege in Privilege.objects.select_related('category').filter(status='active'):
            privileges.append({
                'id': privilege.id,
                'name': privilege.name,
                'category': privilege.category.display_name,
            })

        # 获取所有处理人
        processors = list(
            PrivilegeProcessRecord.objects.values_list('processor', flat=True)
            .distinct()
            .order_by('processor')
        )

        # 获取VIP等级范围
        try:
            from privileges.models import VIPLevel
            vip_levels = []
            for vip in VIPLevel.objects.all().order_by('level'):
                vip_levels.append({
                    'level': vip.level,
                    'name': vip.name,
                    'display': vip.name
                })
        except Exception:
            # 如果查询失败，使用传统格式
            vip_levels = [{'level': i, 'name': f'VIP{i}', 'display': f'VIP{i}'} for i in range(0, 11)]

        return JsonResponse({
            'success': True,
            'data': {
                'privileges': privileges,
                'processors': processors,
                'vip_levels': vip_levels,
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取筛选选项失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def privilege_process_create_api(request):
    """创建特权处理记录API"""
    try:
        data = json.loads(request.body)

        # 验证必填字段
        required_fields = ['character_name', 'character_id', 'vip_level', 'privilege_id', 'process_detail', 'processor']
        for field in required_fields:
            if not data.get(field):
                return JsonResponse({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }, status=400)

        # 检查特权是否存在
        try:
            privilege = Privilege.objects.get(id=data['privilege_id'])
        except Privilege.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': '指定的特权不存在'
            }, status=400)

        # 验证VIP等级范围
        vip_level = int(data['vip_level'])
        if vip_level < 0 or vip_level > 10:
            return JsonResponse({
                'success': False,
                'message': 'VIP等级必须在0-10之间'
            }, status=400)

        # 验证角色ID
        character_id = int(data['character_id'])
        if character_id <= 0:
            return JsonResponse({
                'success': False,
                'message': '角色ID必须大于0'
            }, status=400)

        # 创建处理记录
        record = PrivilegeProcessRecord.objects.create(
            character_name=data['character_name'].strip(),
            character_id=character_id,
            vip_level=vip_level,
            privilege=privilege,
            process_detail=data['process_detail'].strip(),
            processor=data['processor'].strip()
        )

        return JsonResponse({
            'success': True,
            'message': '特权处理记录创建成功',
            'data': {
                'id': record.id,
                'character_name': record.character_name,
                'character_id': record.character_id,
                'privilege_name': record.privilege.name
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': '无效的JSON数据'
        }, status=400)
    except ValueError as e:
        return JsonResponse({
            'success': False,
            'message': f'数据格式错误: {str(e)}'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'创建失败: {str(e)}'
        }, status=500)
