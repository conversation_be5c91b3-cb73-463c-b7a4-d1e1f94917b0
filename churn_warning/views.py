from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.paginator import Paginator
import json
import logging

from .services import ChurnWarningService, ChurnPredictionService

logger = logging.getLogger(__name__)


def churn_warning_center(request):
    """流失预警中心页面视图"""
    context = {
        'page_title': '流失预警中心',
        'page_description': '实时监控大R用户流失风险，及时采取挽留措施',
    }
    return render(request, 'churn_warning/churn_warning.html', context)


@require_http_methods(["GET"])
def risk_stats_api(request):
    """获取风险统计数据API"""
    try:
        result = ChurnWarningService.get_risk_stats()
        
        if result['success']:
            return JsonResponse({
                'success': True,
                'data': result['data']
            })
        else:
            return JsonResponse({
                'success': False,
                'message': result['message']
            }, status=500)
            
    except Exception as e:
        logger.error(f"获取风险统计数据失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'获取统计数据失败: {str(e)}'
        }, status=500)


@require_http_methods(["GET"])
def users_list_api(request):
    """获取用户列表API"""
    try:
        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        risk_level = request.GET.get('risk_level', 'all')
        search_query = request.GET.get('search', '').strip()
        server_filter = request.GET.get('server', '')
        
        # 构建筛选条件
        filters = {
            'risk_level': risk_level,
            'search': search_query,
            'server': server_filter
        }
        
        # 获取用户列表
        result = ChurnWarningService.get_users_list(
            page=page,
            page_size=page_size,
            **filters
        )
        
        if result['success']:
            return JsonResponse({
                'success': True,
                'data': result['data']
            })
        else:
            return JsonResponse({
                'success': False,
                'message': result['message']
            }, status=500)
            
    except ValueError as e:
        return JsonResponse({
            'success': False,
            'message': f'参数错误: {str(e)}'
        }, status=400)
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'获取用户列表失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def batch_contact_api(request):
    """批量联系用户API"""
    try:
        data = json.loads(request.body)
        user_ids = data.get('user_ids', [])
        
        if not user_ids:
            return JsonResponse({
                'success': False,
                'message': '用户ID列表不能为空'
            }, status=400)
        
        # 验证用户ID格式
        try:
            user_ids = [int(uid) for uid in user_ids]
        except (ValueError, TypeError):
            return JsonResponse({
                'success': False,
                'message': '用户ID格式错误'
            }, status=400)
        
        result = ChurnWarningService.batch_contact_users(user_ids)
        
        if result['success']:
            return JsonResponse({
                'success': True,
                'message': result['message'],
                'contacted_count': result['contacted_count']
            })
        else:
            return JsonResponse({
                'success': False,
                'message': result['message']
            }, status=500)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'JSON格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"批量联系用户失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'批量联系失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def batch_gift_api(request):
    """批量赠送礼包API"""
    try:
        data = json.loads(request.body)
        user_ids = data.get('user_ids', [])
        
        if not user_ids:
            return JsonResponse({
                'success': False,
                'message': '用户ID列表不能为空'
            }, status=400)
        
        # 验证用户ID格式
        try:
            user_ids = [int(uid) for uid in user_ids]
        except (ValueError, TypeError):
            return JsonResponse({
                'success': False,
                'message': '用户ID格式错误'
            }, status=400)
        
        result = ChurnWarningService.batch_send_gifts(user_ids)
        
        if result['success']:
            return JsonResponse({
                'success': True,
                'message': result['message'],
                'gift_count': result['gift_count']
            })
        else:
            return JsonResponse({
                'success': False,
                'message': result['message']
            }, status=500)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'JSON格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"批量赠送礼包失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'批量赠送失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def batch_export_api(request):
    """批量导出数据API"""
    try:
        data = json.loads(request.body)
        user_ids = data.get('user_ids', [])
        export_format = data.get('format', 'csv')  # csv, excel
        
        if not user_ids:
            return JsonResponse({
                'success': False,
                'message': '用户ID列表不能为空'
            }, status=400)
        
        # 验证用户ID格式
        try:
            user_ids = [int(uid) for uid in user_ids]
        except (ValueError, TypeError):
            return JsonResponse({
                'success': False,
                'message': '用户ID格式错误'
            }, status=400)
        
        # 模拟导出处理
        # 实际实现中这里应该生成真实的导出文件
        export_filename = f"churn_warning_users_{len(user_ids)}_{export_format}.{export_format}"
        
        return JsonResponse({
            'success': True,
            'message': f'数据导出已准备完成',
            'export_count': len(user_ids),
            'filename': export_filename,
            'download_url': f'/api/churn-warning/download/{export_filename}'  # 模拟下载链接
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'JSON格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"批量导出数据失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }, status=500)


@require_http_methods(["GET"])
def user_detail_api(request, user_id):
    """获取用户详情API"""
    try:
        user_id = int(user_id)
        
        # 获取用户的流失预测详情
        from .models import ChurnPrediction
        from big_r_overview.models import BigRUser
        
        try:
            prediction = ChurnPrediction.objects.select_related('user').get(user__user_id=user_id)
            user = prediction.user
            
            # 获取干预记录
            from .models import ChurnInterventionLog
            interventions = ChurnInterventionLog.objects.filter(
                prediction=prediction
            ).order_by('-created_at')[:10]
            
            intervention_data = []
            for intervention in interventions:
                intervention_data.append({
                    'id': intervention.id,
                    'type': intervention.intervention_type,
                    'type_display': intervention.get_intervention_type_display(),
                    'description': intervention.description,
                    'status': intervention.status,
                    'status_display': intervention.get_status_display(),
                    'executor': intervention.executor,
                    'created_at': intervention.created_at.strftime('%Y-%m-%d %H:%M'),
                    'effectiveness_score': intervention.effectiveness_score
                })
            
            user_detail = {
                'id': user.user_id,
                'username': user.username,
                'character_name': user.character_name,
                'server_name': user.server_name,
                'vip_level': user.vip_level,
                'vip_level_name': user.vip_level_name,
                'total_recharge': float(user.total_recharge),
                'first_recharge_date': user.first_recharge_date.strftime('%Y-%m-%d') if user.first_recharge_date else None,
                'last_recharge_date': user.last_recharge_date.strftime('%Y-%m-%d') if user.last_recharge_date else None,
                'last_login_date': user.last_login_date.strftime('%Y-%m-%d %H:%M') if user.last_login_date else None,
                'total_login_days': user.total_login_days,
                'consecutive_login_days': user.consecutive_login_days,
                'risk_level': prediction.risk_level,
                'risk_level_display': prediction.get_risk_level_display(),
                'risk_score': prediction.risk_score,
                'login_risk_score': prediction.login_risk_score,
                'recharge_risk_score': prediction.recharge_risk_score,
                'activity_risk_score': prediction.activity_risk_score,
                'predicted_churn_date': prediction.predicted_churn_date.strftime('%Y-%m-%d') if prediction.predicted_churn_date else None,
                'confidence_score': prediction.confidence_score,
                'risk_factors': prediction.risk_factors,
                'suggested_actions': prediction.suggested_actions,
                'status': prediction.status,
                'status_display': prediction.get_status_display(),
                'interventions': intervention_data,
                'created_at': prediction.created_at.strftime('%Y-%m-%d %H:%M'),
                'updated_at': prediction.updated_at.strftime('%Y-%m-%d %H:%M')
            }
            
            return JsonResponse({
                'success': True,
                'data': user_detail
            })
            
        except ChurnPrediction.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': '用户流失预测数据不存在'
            }, status=404)
            
    except ValueError:
        return JsonResponse({
            'success': False,
            'message': '用户ID格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"获取用户详情失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'获取用户详情失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def contact_user_api(request, user_id):
    """联系单个用户API"""
    try:
        user_id = int(user_id)
        data = json.loads(request.body)
        message = data.get('message', '')
        
        # 联系用户处理
        result = ChurnWarningService.batch_contact_users([user_id])
        
        if result['success']:
            return JsonResponse({
                'success': True,
                'message': f'成功联系用户 {user_id}'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': result['message']
            }, status=500)
            
    except ValueError:
        return JsonResponse({
            'success': False,
            'message': '用户ID格式错误'
        }, status=400)
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'JSON格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"联系用户失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'联系用户失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def send_gift_api(request, user_id):
    """给单个用户赠送礼包API"""
    try:
        user_id = int(user_id)
        data = json.loads(request.body)
        gift_type = data.get('gift_type', 'default')
        
        # 赠送礼包处理
        result = ChurnWarningService.batch_send_gifts([user_id])
        
        if result['success']:
            return JsonResponse({
                'success': True,
                'message': f'成功为用户 {user_id} 准备礼包'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': result['message']
            }, status=500)
            
    except ValueError:
        return JsonResponse({
            'success': False,
            'message': '用户ID格式错误'
        }, status=400)
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'JSON格式错误'
        }, status=400)
    except Exception as e:
        logger.error(f"赠送礼包失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'赠送礼包失败: {str(e)}'
        }, status=500)


@require_http_methods(["POST"])
def update_risk_scores_api(request):
    """更新风险评分API（管理员功能）"""
    try:
        # 触发风险评分计算
        result = ChurnPredictionService.calculate_user_risk_scores()
        
        if result['success']:
            return JsonResponse({
                'success': True,
                'message': result['message'],
                'updated_count': result['updated_count']
            })
        else:
            return JsonResponse({
                'success': False,
                'message': result['message']
            }, status=500)
            
    except Exception as e:
        logger.error(f"更新风险评分失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'更新风险评分失败: {str(e)}'
        }, status=500)


def user_detail_page(request, user_id):
    """用户详情页面视图"""
    context = {
        'page_title': '用户详情',
        'page_description': '查看用户详细信息和流失风险分析',
        'user_id': user_id,
    }
    return render(request, 'churn_warning/user_detail.html', context)