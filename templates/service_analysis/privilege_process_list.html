{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
/* 苹果风格特权处理列表页面样式 */
.process-container {
    padding: 20px;
    min-height: 100vh;
    margin: 0;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.9) 100%);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.process-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.process-container > * {
    position: relative;
    z-index: 1;
}

/* 页面头部 */
.page-header {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 20px 40px -12px rgba(0, 0, 0, 0.08),
        0 8px 16px -8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-header:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 12px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        rgba(139, 92, 246, 0.8) 0%, 
        rgba(124, 58, 237, 0.8) 50%, 
        rgba(16, 185, 129, 0.8) 100%);
    border-radius: 16px 16px 0 0;
}

.page-title {
    font-size: 1.4rem;
    font-weight: 800;
    margin-bottom: 8px;
    background: linear-gradient(135deg, 
        rgba(139, 92, 246, 0.9) 0%, 
        rgba(124, 58, 237, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.page-description {
    color: rgba(100, 116, 139, 0.8);
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
    line-height: 1.5;
}

/* 工具栏 */
.toolbar {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 8px 24px -4px rgba(0, 0, 0, 0.08),
        0 4px 12px -4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toolbar-search {
    flex: 1;
    min-width: 300px;
}

.toolbar-search input {
    width: 100%;
    padding: 10px 12px 10px 36px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.toolbar-search input:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.12),
        0 4px 8px -2px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.toolbar-search {
    position: relative;
}

.toolbar-search::before {
    content: '\F52A';
    font-family: 'bootstrap-icons';
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(100, 116, 139, 0.6);
    font-size: 1rem;
    z-index: 1;
}

.toolbar-filters {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.filter-select {
    padding: 10px 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 0.85rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-weight: 500;
    cursor: pointer;
    min-width: 120px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.filter-select:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.12),
        0 4px 8px -2px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.toolbar-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 处理记录列表 */
.records-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 
        0 8px 24px -4px rgba(0, 0, 0, 0.08),
        0 4px 12px -4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.records-header {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(15px);
}

.records-title {
    font-size: 1rem;
    font-weight: 700;
    color: rgba(15, 23, 42, 0.9);
    margin: 0;
    letter-spacing: -0.02em;
}

.records-list {
    max-height: 700px;
    overflow-y: auto;
}

.record-item {
    padding: 20px;
    margin-bottom: 16px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(226, 232, 240, 0.4);
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 
        0 4px 12px -2px rgba(0, 0, 0, 0.06),
        0 2px 6px -2px rgba(0, 0, 0, 0.03),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.record-item:hover {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    transform: translateY(-2px);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 
        0 8px 20px -4px rgba(0, 0, 0, 0.12),
        0 4px 12px -4px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.record-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    flex-wrap: wrap;
    gap: 12px;
}

.character-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.character-name {
    font-size: 0.95rem;
    font-weight: 700;
    color: rgba(15, 23, 42, 0.9);
    letter-spacing: -0.01em;
}

.character-id {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.8rem;
    color: rgba(100, 116, 139, 0.8);
    background: rgba(248, 250, 252, 0.8);
    padding: 3px 8px;
    border-radius: 8px;
    font-weight: 500;
    border: 1px solid rgba(226, 232, 240, 0.5);
}

.vip-badge {
    background: linear-gradient(135deg, 
        rgba(251, 191, 36, 0.9) 0%, 
        rgba(245, 158, 11, 1) 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 700;
    box-shadow: 
        0 2px 4px -1px rgba(245, 158, 11, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.privilege-type-badge {
    background: linear-gradient(135deg, 
        rgba(139, 92, 246, 0.9) 0%, 
        rgba(124, 58, 237, 1) 100%);
    color: white;
    padding: 3px 6px;
    border-radius: 6px;
    font-size: 0.65rem;
    font-weight: 600;
    box-shadow: 
        0 2px 4px -1px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    white-space: nowrap;
}

.record-time {
    font-size: 0.8rem;
    color: rgba(100, 116, 139, 0.8);
    font-weight: 500;
    background: rgba(236, 253, 245, 0.9);
    backdrop-filter: blur(10px);
    padding: 6px 12px;
    border-radius: 12px;
    border: 1px solid rgba(209, 250, 229, 0.5);
    box-shadow: 
        0 2px 4px -1px rgba(16, 185, 129, 0.08),
        0 1px 2px -1px rgba(16, 185, 129, 0.04);
}

.record-content {
    margin-bottom: var(--spacing-sm);
}

.privilege-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.4);
    backdrop-filter: blur(8px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.record-item:hover .privilege-info {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px -2px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.privilege-icon {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.9) 0%, 
        rgba(37, 99, 235, 1) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.3),
        0 2px 4px -2px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
}

.record-item:hover .privilege-icon {
    transform: scale(1.05);
    box-shadow: 
        0 6px 12px -2px rgba(59, 130, 246, 0.4),
        0 4px 8px -2px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.privilege-text {
    flex: 1;
    min-width: 0;
}

.privilege-name-with-desc {
    font-weight: 500;
    color: rgba(15, 23, 42, 0.9);
    font-size: 0.85rem;
    letter-spacing: -0.01em;
    line-height: 1.4;
    word-break: break-word;
}

.privilege-name {
    font-weight: 600;
    color: rgba(15, 23, 42, 0.9);
}

.privilege-desc {
    font-weight: 400;
    color: rgba(71, 85, 105, 0.8);
}

.privilege-category {
    font-size: 0.75rem;
    color: rgba(100, 116, 139, 0.8);
    font-weight: 500;
    background: rgba(100, 116, 139, 0.1);
    padding: 2px 8px;
    border-radius: 6px;
    border: 1px solid rgba(100, 116, 139, 0.2);
}

.process-detail {
    color: rgba(71, 85, 105, 0.8);
    font-size: 0.85rem;
    line-height: 1.6;
    margin-bottom: 12px;
    background: rgba(248, 250, 252, 0.6);
    padding: 12px;
    border-radius: 10px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    backdrop-filter: blur(5px);
}

.record-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.8rem;
    color: rgba(100, 116, 139, 0.8);
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(226, 232, 240, 0.3);
}

.processor-info {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(243, 244, 246, 0.8);
    padding: 6px 10px;
    border-radius: 8px;
    font-weight: 500;
}

/* 加载状态 */
.loading-container {
    text-align: center;
    padding: 48px;
    color: rgba(100, 116, 139, 0.8);
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    border-radius: 12px;
    margin: 20px;
}

.loading-spinner {
    display: inline-block;
    width: 36px;
    height: 36px;
    border: 3px solid rgba(226, 232, 240, 0.3);
    border-top: 3px solid rgba(59, 130, 246, 0.9);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.2),
        0 2px 4px -2px rgba(59, 130, 246, 0.1);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 48px;
    color: rgba(100, 116, 139, 0.8);
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    border-radius: 12px;
    margin: 20px;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.4;
    color: rgba(59, 130, 246, 0.6);
}

/* 分页 */
.pagination-container {
    padding: 16px 20px;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(248, 250, 252, 0.6);
    backdrop-filter: blur(10px);
}

.pagination-info {
    font-size: 0.8rem;
    color: rgba(100, 116, 139, 0.8);
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.pagination-btn {
    padding: 8px 14px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 4px;
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.pagination-btn:hover:not(:disabled) {
    background: rgba(59, 130, 246, 0.9);
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.3),
        0 2px 4px -2px rgba(59, 130, 246, 0.2);
}

.pagination-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: rgba(248, 250, 252, 0.6);
    color: rgba(156, 163, 175, 0.8);
    border-color: rgba(229, 231, 235, 0.6);
}

/* 按钮样式 */
.action-btn {
    padding: 10px 16px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-btn:hover {
    background: rgba(248, 250, 252, 0.9);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px -2px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-btn.primary {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.9) 0%, 
        rgba(37, 99, 235, 1) 100%);
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.3),
        0 2px 4px -2px rgba(59, 130, 246, 0.2);
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, 
        rgba(37, 99, 235, 0.9) 0%, 
        rgba(29, 78, 216, 1) 100%);
    box-shadow: 
        0 8px 16px -4px rgba(59, 130, 246, 0.4),
        0 4px 8px -4px rgba(59, 130, 246, 0.3);
}

.action-btn.secondary {
    background: linear-gradient(135deg, 
        rgba(100, 116, 139, 0.9) 0%, 
        rgba(71, 85, 105, 1) 100%);
    color: white;
    border-color: rgba(100, 116, 139, 0.3);
    box-shadow: 
        0 4px 8px -2px rgba(100, 116, 139, 0.3),
        0 2px 4px -2px rgba(100, 116, 139, 0.2);
}

.action-btn.secondary:hover {
    background: linear-gradient(135deg, 
        rgba(71, 85, 105, 0.9) 0%, 
        rgba(51, 65, 85, 1) 100%);
    box-shadow: 
        0 8px 16px -4px rgba(100, 116, 139, 0.4),
        0 4px 8px -4px rgba(100, 116, 139, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .process-container {
        padding: 16px;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-search {
        min-width: auto;
    }
    
    .toolbar-filters {
        justify-content: center;
    }
    
    .record-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .character-info {
        flex-wrap: wrap;
    }
    
    .record-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 8px 16px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px 16px 0 0;
}

.modal-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: rgba(15, 23, 42, 0.9);
    margin: 0;
    letter-spacing: -0.02em;
}

.modal-close {
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.6);
    color: rgba(100, 116, 139, 0.8);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(239, 68, 68, 0.1);
    color: rgba(239, 68, 68, 0.9);
    border-color: rgba(239, 68, 68, 0.3);
    transform: scale(1.05);
}

.modal-form {
    padding: 20px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 0 0 16px 16px;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.8rem;
    font-weight: 600;
    color: rgba(15, 23, 42, 0.9);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 10px;
    font-size: 0.85rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    color: rgba(15, 23, 42, 0.9);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.12),
        0 4px 8px -2px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 按钮加载状态 */
.btn-loading .spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 响应式模态框 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-container {
        width: 95%;
        margin: 16px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="process-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">{{ page_title }}</h1>
        <p class="page-description">{{ page_description }}</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
        <div class="toolbar-search">
            <input type="text" id="searchInput" placeholder="搜索角色名、角色ID、处理详情...">
        </div>
        
        <div class="toolbar-filters">
            <select id="privilegeFilter" class="filter-select">
                <option value="">所有特权类型</option>
            </select>
            
            <select id="vipLevelFilter" class="filter-select">
                <option value="">所有VIP等级</option>
            </select>
            
            <select id="processorFilter" class="filter-select">
                <option value="">所有处理人</option>
            </select>
            
            <input type="date" id="dateFromFilter" class="filter-select">
            <input type="date" id="dateToFilter" class="filter-select">
        </div>
        
        <div class="toolbar-actions">
            <button class="action-btn primary">
                <i class="bi bi-plus"></i>
                添加记录
            </button>
            <button class="action-btn secondary">
                <i class="bi bi-download"></i>
                导出数据
            </button>
        </div>
    </div>

    <!-- 处理记录列表 -->
    <div class="records-container">
        <div class="records-header">
            <h2 class="records-title">特权处理记录</h2>
        </div>
        
        <div class="records-list" id="recordsList">
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingContainer">
                <div class="loading-spinner"></div>
                <div>正在加载处理记录...</div>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="bi bi-inbox"></i>
                </div>
                <h3>暂无处理记录</h3>
                <p>没有找到符合条件的特权处理记录</p>
            </div>
            
            <!-- 动态加载的处理记录将在这里显示 -->
        </div>
        
        <!-- 分页 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo">
                显示第 1-20 条，共 100 条记录
            </div>
            <div class="pagination-controls">
                <button class="pagination-btn" id="prevPageBtn">上一页</button>
                <button class="pagination-btn" id="nextPageBtn">下一页</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加记录模态框 -->
<div class="modal-overlay" id="addRecordModal" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h2 class="modal-title">添加特权处理记录</h2>
            <button class="modal-close" id="modalClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <form class="modal-form" id="addRecordForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="characterName">角色名 *</label>
                    <input type="text" id="characterName" name="character_name" required maxlength="100" placeholder="请输入角色名">
                </div>

                <div class="form-group">
                    <label for="characterId">角色ID *</label>
                    <input type="number" id="characterId" name="character_id" required min="1" placeholder="请输入角色ID">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="vipLevel">VIP等级 *</label>
                    <select id="vipLevel" name="vip_level" required>
                        <option value="">请选择VIP等级</option>
                        <!-- 动态填充VIP等级选项 -->
                    </select>
                </div>

                <div class="form-group">
                    <label for="privilegeType">特权类型 *</label>
                    <select id="privilegeType" name="privilege_id" required>
                        <option value="">请选择特权类型</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="processDetail">处理详情 *</label>
                <textarea id="processDetail" name="process_detail" required rows="4" placeholder="请详细描述特权处理的具体内容..."></textarea>
            </div>

            <div class="form-group">
                <label for="processor">处理人 *</label>
                <input type="text" id="processor" name="processor" required maxlength="100" placeholder="请输入处理人姓名">
            </div>
        </form>

        <div class="modal-footer">
            <button type="button" class="action-btn secondary" id="modalCancel">取消</button>
            <button type="submit" class="action-btn primary" id="modalSubmit" form="addRecordForm">
                <span class="btn-text">添加记录</span>
                <span class="btn-loading" style="display: none;">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    添加中...
                </span>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 特权处理列表页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 特权处理列表页面加载');
    
    let currentPage = 1;
    let totalPages = 1;
    
    // 初始化页面
    initializePage();

    function initializePage() {
        // 加载筛选选项
        loadFilterOptions();

        // 加载处理记录
        loadRecords();

        // 绑定搜索事件
        bindSearchEvents();

        // 绑定筛选事件
        bindFilterEvents();

        // 绑定分页事件
        bindPaginationEvents();

        // 初始化模态框
        initializeModal();

        console.log('✅ 页面初始化完成');
    }

    // 初始化模态框
    function initializeModal() {
        const addRecordModal = document.getElementById('addRecordModal');
        const modalClose = document.getElementById('modalClose');
        const modalCancel = document.getElementById('modalCancel');
        const addRecordForm = document.getElementById('addRecordForm');
        const addRecordBtn = document.querySelector('.toolbar-actions .action-btn.primary');

        // 绑定添加记录按钮事件
        console.log('🔗 添加记录按钮:', addRecordBtn);
        if (addRecordBtn) {
            addRecordBtn.addEventListener('click', showAddRecordModal);
            console.log('✅ 添加记录按钮事件已绑定');
        } else {
            console.error('❌ 找不到添加记录按钮');
        }

        // 关闭模态框事件
        [modalClose, modalCancel].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', hideAddRecordModal);
            }
        });

        // 点击遮罩关闭模态框
        if (addRecordModal) {
            addRecordModal.addEventListener('click', (e) => {
                if (e.target === addRecordModal) {
                    hideAddRecordModal();
                }
            });
        }

        // 表单提交
        if (addRecordForm) {
            addRecordForm.addEventListener('submit', handleFormSubmit);
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && addRecordModal.classList.contains('show')) {
                hideAddRecordModal();
            }
        });
    }
    
    // 加载筛选选项
    async function loadFilterOptions() {
        try {
            const response = await fetch('/service-analysis/api/filter-options/');
            const data = await response.json();
            
            if (data.success) {
                populateFilterOptions(data.data);
                console.log('✅ 筛选选项加载成功');
            } else {
                console.error('❌ 筛选选项加载失败:', data.message);
            }
        } catch (error) {
            console.error('❌ 筛选选项加载失败:', error);
        }
    }
    
    // 填充筛选选项
    function populateFilterOptions(options) {
        // 填充特权类型选项
        const privilegeFilter = document.getElementById('privilegeFilter');
        if (privilegeFilter && options.privileges) {
            privilegeFilter.innerHTML = '<option value="">所有特权类型</option>';
            options.privileges.forEach(privilege => {
                const option = document.createElement('option');
                option.value = privilege.id;
                option.textContent = `${privilege.name} (${privilege.category})`;
                privilegeFilter.appendChild(option);
            });
        }

        // 填充VIP等级选项
        const vipLevelFilter = document.getElementById('vipLevelFilter');
        if (vipLevelFilter && options.vip_levels) {
            vipLevelFilter.innerHTML = '<option value="">所有VIP等级</option>';
            options.vip_levels.forEach(level => {
                const option = document.createElement('option');
                option.value = typeof level === 'object' ? level.level : level;
                option.textContent = typeof level === 'object' ? level.display : (level === 0 ? '普通用户' : `VIP${level}`);
                vipLevelFilter.appendChild(option);
            });
        }

        // 填充处理人选项
        const processorFilter = document.getElementById('processorFilter');
        if (processorFilter && options.processors) {
            processorFilter.innerHTML = '<option value="">所有处理人</option>';
            options.processors.forEach(processor => {
                const option = document.createElement('option');
                option.value = processor;
                option.textContent = processor;
                processorFilter.appendChild(option);
            });
        }

        // 填充模态框中的特权类型选项
        const privilegeTypeSelect = document.getElementById('privilegeType');
        if (privilegeTypeSelect && options.privileges) {
            privilegeTypeSelect.innerHTML = '<option value="">请选择特权类型</option>';
            options.privileges.forEach(privilege => {
                const option = document.createElement('option');
                option.value = privilege.id;
                option.textContent = `${privilege.name} (${privilege.category})`;
                privilegeTypeSelect.appendChild(option);
            });
        }

        // 填充模态框中的VIP等级选项
        const vipLevelSelect = document.getElementById('vipLevel');
        if (vipLevelSelect && options.vip_levels) {
            vipLevelSelect.innerHTML = '<option value="">请选择VIP等级</option>';
            options.vip_levels.forEach(level => {
                const option = document.createElement('option');
                option.value = typeof level === 'object' ? level.level : level;
                option.textContent = typeof level === 'object' ? level.display : (level === 0 ? '普通用户' : `VIP${level}`);
                vipLevelSelect.appendChild(option);
            });
        }
    }

    // 显示添加记录模态框
    function showAddRecordModal() {
        console.log('🆕 显示添加记录模态框');

        // 重置表单
        resetForm();

        // 显示模态框
        showModal('addRecordModal');
    }

    // 隐藏添加记录模态框
    function hideAddRecordModal() {
        hideModal('addRecordModal');
    }

    // 显示模态框
    function showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
            setTimeout(() => modal.classList.add('show'), 10);
            document.body.style.overflow = 'hidden';
        }
    }

    // 隐藏模态框
    function hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        }
    }

    // 重置表单
    function resetForm() {
        const form = document.getElementById('addRecordForm');
        if (form) {
            form.reset();
        }
    }

    // 表单提交处理
    async function handleFormSubmit(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('modalSubmit');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        // 显示加载状态
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
        submitBtn.disabled = true;

        try {
            // 收集表单数据
            const formData = new FormData(e.target);
            const data = {};

            for (let [key, value] of formData.entries()) {
                data[key] = value.trim();
            }

            console.log('📤 提交数据:', data);

            // 发送请求
            const response = await fetch('/service-analysis/api/create/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                showNotification(result.message, 'success');
                hideAddRecordModal();

                // 重新加载数据
                loadRecords(1);
            } else {
                showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('❌ 表单提交失败:', error);
            showNotification('添加失败，请稍后重试', 'error');
        } finally {
            // 恢复按钮状态
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            submitBtn.disabled = false;
        }
    }

    // 获取CSRF Token
    function getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="bi bi-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-lg);
            z-index: 1100;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        // 根据类型设置颜色
        if (type === 'success') {
            notification.style.borderLeftColor = 'var(--success)';
        } else if (type === 'error') {
            notification.style.borderLeftColor = 'var(--error)';
        } else if (type === 'warning') {
            notification.style.borderLeftColor = 'var(--warning)';
        } else {
            notification.style.borderLeftColor = 'var(--info)';
        }

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动移除
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 获取通知图标
    function getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'x-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    // 加载处理记录
    async function loadRecords(page = 1) {
        try {
            showLoading();
            
            const params = new URLSearchParams({
                search: document.getElementById('searchInput')?.value || '',
                privilege_type: document.getElementById('privilegeFilter')?.value || '',
                vip_level: document.getElementById('vipLevelFilter')?.value || '',
                processor: document.getElementById('processorFilter')?.value || '',
                date_from: document.getElementById('dateFromFilter')?.value || '',
                date_to: document.getElementById('dateToFilter')?.value || '',
                page: page,
                page_size: 20
            });
            
            const response = await fetch(`/service-analysis/api/records/?${params}`);
            const data = await response.json();
            
            if (data.success) {
                hideLoading();
                
                if (data.data.records.length === 0) {
                    showEmptyState();
                    hidePagination();
                } else {
                    hideEmptyState();
                    renderRecords(data.data.records);
                    updatePagination(data.data.pagination);
                }
                
                console.log('✅ 处理记录加载成功:', data.data.records.length, '条记录');
            } else {
                hideLoading();
                showEmptyState();
                hidePagination();
                console.error('❌ 处理记录加载失败:', data.message);
            }
        } catch (error) {
            hideLoading();
            showEmptyState();
            hidePagination();
            console.error('❌ 处理记录加载失败:', error);
        }
    }
    
    // 渲染处理记录
    function renderRecords(records) {
        const recordsList = document.getElementById('recordsList');
        
        // 清空现有内容（保留加载和空状态元素）
        const existingRecords = recordsList.querySelectorAll('.record-item');
        existingRecords.forEach(record => record.remove());
        
        // 添加新的记录项
        records.forEach(record => {
            const recordElement = createRecordElement(record);
            recordsList.appendChild(recordElement);
        });
        
        console.log(`✅ 渲染了 ${records.length} 条处理记录`);
    }
    
    // 创建记录元素
    function createRecordElement(record) {
        const recordDiv = document.createElement('div');
        recordDiv.className = 'record-item';
        recordDiv.setAttribute('data-record-id', record.id);
        
        const timeAgo = getTimeAgo(record.processed_at);
        const vipDisplay = record.vip_level_name || (record.vip_level === 0 ? '普通用户' : `VIP${record.vip_level}`);
        
        recordDiv.innerHTML = `
            <div class="record-header">
                <div class="character-info">
                    <div class="character-name">${record.character_name}</div>
                    <div class="character-id">${record.character_id}</div>
                    <div class="vip-badge">${vipDisplay}</div>
                    <div class="privilege-type-badge">${record.privilege.category}</div>
                </div>
                <div class="record-time">${timeAgo}</div>
            </div>
            <div class="record-content">
                <div class="privilege-info">
                    <div class="privilege-icon">
                        <i class="${record.privilege.icon}"></i>
                    </div>
                    <div class="privilege-text">
                        <div class="privilege-name-with-desc">
                            <span class="privilege-name">${record.privilege.name}</span>: <span class="privilege-desc">${record.privilege.description || '暂无描述'}</span>
                        </div>
                    </div>
                </div>
                <div class="process-detail">${record.process_detail}</div>
            </div>
            <div class="record-footer">
                <div class="processor-info">
                    <i class="bi bi-person-check"></i>
                    <span>处理人: ${record.processor}</span>
                </div>
                <div class="process-time">
                    <i class="bi bi-clock"></i>
                    <span>${new Date(record.processed_at).toLocaleString()}</span>
                </div>
            </div>
        `;
        
        // 添加点击事件
        recordDiv.addEventListener('click', () => {
            console.log('点击处理记录:', record.id);
            // TODO: 打开记录详情
        });
        
        return recordDiv;
    }
    
    // 获取相对时间
    function getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMins < 60) {
            return `${diffMins}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return date.toLocaleDateString();
        }
    }
    
    // 更新分页
    function updatePagination(pagination) {
        currentPage = pagination.current_page;
        totalPages = pagination.total_pages;
        
        const paginationContainer = document.getElementById('paginationContainer');
        const paginationInfo = document.getElementById('paginationInfo');
        const prevPageBtn = document.getElementById('prevPageBtn');
        const nextPageBtn = document.getElementById('nextPageBtn');
        
        if (pagination.total_count > 0) {
            paginationContainer.style.display = 'flex';
            
            const startIndex = (currentPage - 1) * 20 + 1;
            const endIndex = Math.min(currentPage * 20, pagination.total_count);
            paginationInfo.textContent = `显示第 ${startIndex}-${endIndex} 条，共 ${pagination.total_count} 条记录`;
            
            prevPageBtn.disabled = !pagination.has_previous;
            nextPageBtn.disabled = !pagination.has_next;
        } else {
            paginationContainer.style.display = 'none';
        }
    }
    
    // 显示加载状态
    function showLoading() {
        const loadingContainer = document.getElementById('loadingContainer');
        const emptyState = document.getElementById('emptyState');
        
        if (loadingContainer) loadingContainer.style.display = 'block';
        if (emptyState) emptyState.style.display = 'none';
        
        // 清空现有记录
        const existingRecords = document.querySelectorAll('.record-item');
        existingRecords.forEach(record => record.remove());
    }
    
    // 隐藏加载状态
    function hideLoading() {
        const loadingContainer = document.getElementById('loadingContainer');
        if (loadingContainer) loadingContainer.style.display = 'none';
    }
    
    // 显示空状态
    function showEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'block';
    }
    
    // 隐藏空状态
    function hideEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'none';
    }
    
    // 隐藏分页
    function hidePagination() {
        const paginationContainer = document.getElementById('paginationContainer');
        if (paginationContainer) paginationContainer.style.display = 'none';
    }
    
    // 绑定搜索事件
    function bindSearchEvents() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
        }
    }
    
    // 绑定筛选事件
    function bindFilterEvents() {
        const filters = ['privilegeFilter', 'vipLevelFilter', 'processorFilter', 'dateFromFilter', 'dateToFilter'];
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', handleFilter);
            }
        });
    }
    
    // 绑定分页事件
    function bindPaginationEvents() {
        const prevPageBtn = document.getElementById('prevPageBtn');
        const nextPageBtn = document.getElementById('nextPageBtn');
        
        if (prevPageBtn) {
            prevPageBtn.addEventListener('click', () => {
                if (currentPage > 1) {
                    loadRecords(currentPage - 1);
                }
            });
        }
        
        if (nextPageBtn) {
            nextPageBtn.addEventListener('click', () => {
                if (currentPage < totalPages) {
                    loadRecords(currentPage + 1);
                }
            });
        }
    }
    
    // 搜索处理
    function handleSearch() {
        currentPage = 1;
        loadRecords(1);
    }
    
    // 筛选处理
    function handleFilter() {
        currentPage = 1;
        loadRecords(1);
    }
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});
</script>
{% endblock %}
