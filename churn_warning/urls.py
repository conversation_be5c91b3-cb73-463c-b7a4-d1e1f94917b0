from django.urls import path
from . import views

app_name = 'churn_warning'

urlpatterns = [
    # 页面视图
    path('', views.churn_warning_center, name='center'),
    path('user/<int:user_id>/', views.user_detail_page, name='user_detail'),
    
    # API接口
    path('api/stats/', views.risk_stats_api, name='risk_stats_api'),
    path('api/users/', views.users_list_api, name='users_list_api'),
    path('api/users/<int:user_id>/', views.user_detail_api, name='user_detail_api'),
    
    # 批量操作API
    path('api/batch-contact/', views.batch_contact_api, name='batch_contact_api'),
    path('api/batch-gift/', views.batch_gift_api, name='batch_gift_api'),
    path('api/batch-export/', views.batch_export_api, name='batch_export_api'),
    
    # 单用户操作API
    path('api/users/<int:user_id>/contact/', views.contact_user_api, name='contact_user_api'),
    path('api/users/<int:user_id>/gift/', views.send_gift_api, name='send_gift_api'),
    
    # 管理功能API
    path('api/update-scores/', views.update_risk_scores_api, name='update_risk_scores_api'),
]