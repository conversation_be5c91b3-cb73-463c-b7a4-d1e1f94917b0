# Generated by Django 5.2.4 on 2025-08-06 03:31

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('big_r_overview', '0002_alter_bigruser_created_at'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChurnRiskFactor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('factor_type', models.CharField(choices=[('login_frequency', '登录频率'), ('login_duration', '登录时长'), ('recharge_frequency', '充值频率'), ('recharge_amount', '充值金额'), ('battle_activity', '战斗活跃度'), ('social_activity', '社交活跃度'), ('item_usage', '道具使用'), ('level_progress', '等级进度')], max_length=50, unique=True, verbose_name='风险因子类型')),
                ('description', models.TextField(verbose_name='因子描述')),
                ('weight', models.FloatField(default=0.1, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='权重(0-1)')),
                ('threshold_value', models.FloatField(default=0.0, verbose_name='阈值')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '流失风险因子',
                'verbose_name_plural': '流失风险因子',
                'ordering': ['-weight', 'factor_type'],
            },
        ),
        migrations.CreateModel(
            name='ChurnWarningConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('critical_threshold', models.FloatField(default=80.0, verbose_name='严重风险阈值')),
                ('high_threshold', models.FloatField(default=60.0, verbose_name='高风险阈值')),
                ('medium_threshold', models.FloatField(default=40.0, verbose_name='中等风险阈值')),
                ('prediction_days', models.IntegerField(default=30, verbose_name='预测天数')),
                ('min_confidence_score', models.FloatField(default=0.7, verbose_name='最低置信度')),
                ('auto_contact_critical', models.BooleanField(default=True, verbose_name='自动联系严重风险用户')),
                ('auto_gift_threshold', models.FloatField(default=85.0, verbose_name='自动赠送礼包阈值')),
                ('stats_retention_days', models.IntegerField(default=90, verbose_name='统计数据保留天数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '流失预警配置',
                'verbose_name_plural': '流失预警配置',
            },
        ),
        migrations.CreateModel(
            name='ChurnWarningStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stat_date', models.DateField(unique=True, verbose_name='统计日期')),
                ('critical_users', models.IntegerField(default=0, verbose_name='严重风险用户数')),
                ('high_risk_users', models.IntegerField(default=0, verbose_name='高风险用户数')),
                ('medium_risk_users', models.IntegerField(default=0, verbose_name='中等风险用户数')),
                ('low_risk_users', models.IntegerField(default=0, verbose_name='低风险用户数')),
                ('contacted_users', models.IntegerField(default=0, verbose_name='已联系用户数')),
                ('intervened_users', models.IntegerField(default=0, verbose_name='已干预用户数')),
                ('recovered_users', models.IntegerField(default=0, verbose_name='已挽回用户数')),
                ('churned_users', models.IntegerField(default=0, verbose_name='已流失用户数')),
                ('intervention_success_rate', models.FloatField(default=0.0, verbose_name='干预成功率')),
                ('avg_risk_score', models.FloatField(default=0.0, verbose_name='平均风险评分')),
                ('critical_change', models.IntegerField(default=0, verbose_name='严重风险用户变化')),
                ('high_risk_change', models.IntegerField(default=0, verbose_name='高风险用户变化')),
                ('medium_risk_change', models.IntegerField(default=0, verbose_name='中等风险用户变化')),
                ('low_risk_change', models.IntegerField(default=0, verbose_name='低风险用户变化')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '流失预警统计',
                'verbose_name_plural': '流失预警统计',
                'ordering': ['-stat_date'],
            },
        ),
        migrations.CreateModel(
            name='ChurnPrediction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('risk_level', models.CharField(choices=[('low', '低风险'), ('medium', '中等风险'), ('high', '高风险'), ('critical', '严重风险')], max_length=10, verbose_name='风险等级')),
                ('risk_score', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)], verbose_name='风险评分(0-100)')),
                ('login_risk_score', models.FloatField(default=0.0, verbose_name='登录风险评分')),
                ('recharge_risk_score', models.FloatField(default=0.0, verbose_name='充值风险评分')),
                ('battle_risk_score', models.FloatField(default=0.0, verbose_name='战斗风险评分')),
                ('social_risk_score', models.FloatField(default=0.0, verbose_name='社交风险评分')),
                ('predicted_churn_date', models.DateField(blank=True, null=True, verbose_name='预测流失日期')),
                ('confidence_score', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)], verbose_name='预测置信度')),
                ('risk_factors', models.JSONField(default=list, verbose_name='风险因素')),
                ('suggested_actions', models.JSONField(default=list, verbose_name='建议措施')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('contacted', '已联系'), ('intervened', '已干预'), ('recovered', '已挽回'), ('churned', '已流失')], default='pending', max_length=20, verbose_name='处理状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='big_r_overview.bigruser', verbose_name='用户')),
            ],
            options={
                'verbose_name': '流失预测',
                'verbose_name_plural': '流失预测',
                'ordering': ['-risk_score', '-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='ChurnInterventionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('intervention_type', models.CharField(choices=[('contact', '联系用户'), ('gift', '赠送礼包'), ('discount', '折扣优惠'), ('service', '客服介入'), ('event', '活动邀请'), ('other', '其他方式')], max_length=20, verbose_name='干预类型')),
                ('description', models.TextField(verbose_name='干预描述')),
                ('status', models.CharField(choices=[('planned', '计划中'), ('executing', '执行中'), ('completed', '已完成'), ('failed', '失败')], default='planned', max_length=20, verbose_name='状态')),
                ('executor', models.CharField(blank=True, max_length=100, verbose_name='执行人')),
                ('execution_date', models.DateTimeField(blank=True, null=True, verbose_name='执行时间')),
                ('completion_date', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('effectiveness_score', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='效果评分(1-5)')),
                ('user_response', models.TextField(blank=True, verbose_name='用户反应')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('prediction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='churn_warning.churnprediction', verbose_name='流失预测')),
            ],
            options={
                'verbose_name': '干预记录',
                'verbose_name_plural': '干预记录',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='churnprediction',
            index=models.Index(fields=['risk_level'], name='churn_warni_risk_le_0ad497_idx'),
        ),
        migrations.AddIndex(
            model_name='churnprediction',
            index=models.Index(fields=['risk_score'], name='churn_warni_risk_sc_27d0fd_idx'),
        ),
        migrations.AddIndex(
            model_name='churnprediction',
            index=models.Index(fields=['status'], name='churn_warni_status_41a166_idx'),
        ),
        migrations.AddIndex(
            model_name='churnprediction',
            index=models.Index(fields=['created_at'], name='churn_warni_created_61fc5a_idx'),
        ),
        migrations.AddIndex(
            model_name='churninterventionlog',
            index=models.Index(fields=['prediction', 'intervention_type'], name='churn_warni_predict_2e9860_idx'),
        ),
        migrations.AddIndex(
            model_name='churninterventionlog',
            index=models.Index(fields=['status'], name='churn_warni_status_82417e_idx'),
        ),
        migrations.AddIndex(
            model_name='churninterventionlog',
            index=models.Index(fields=['execution_date'], name='churn_warni_executi_c35d44_idx'),
        ),
    ]
