from django.contrib import admin
from .models import ChurnRiskFactor, ChurnPrediction, ChurnWarningStats


@admin.register(ChurnRiskFactor)
class ChurnRiskFactorAdmin(admin.ModelAdmin):
    list_display = ['factor_type', 'weight', 'threshold_value', 'is_active']
    list_filter = ['factor_type', 'is_active']
    search_fields = ['factor_type', 'description']
    ordering = ['-weight', 'factor_type']


@admin.register(ChurnPrediction)
class ChurnPredictionAdmin(admin.ModelAdmin):
    list_display = ['user', 'risk_level', 'risk_score', 'predicted_churn_date', 'created_at']
    list_filter = ['risk_level', 'created_at']
    search_fields = ['user__character_name', 'user__username']
    ordering = ['-risk_score', '-created_at']
    readonly_fields = ['risk_score', 'predicted_churn_date', 'created_at', 'updated_at']


@admin.register(ChurnWarningStats)
class ChurnWarningStatsAdmin(admin.ModelAdmin):
    list_display = ['stat_date', 'critical_users', 'high_risk_users', 'medium_risk_users', 'low_risk_users']
    list_filter = ['stat_date']
    ordering = ['-stat_date']
    readonly_fields = ['created_at', 'updated_at']