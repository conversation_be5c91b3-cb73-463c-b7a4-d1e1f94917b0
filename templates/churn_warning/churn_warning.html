{% extends 'base.html' %}
{% load static %}

{% block title %}流失预警中心 - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}流失预警中心{% endblock %}

{% block extra_css %}
<style>
/* 苹果风格流失预警中心页面样式 */
.churn-warning-container {
    padding: 20px;
    min-height: 100vh;
    margin: 0;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-radius: 0;
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.08),
        0 8px 16px -8px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: none;
    position: relative;
    overflow: hidden;
}

.churn-warning-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(239, 68, 68, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.churn-warning-container > * {
    position: relative;
    z-index: 1;
}

/* 页面头部 */
.page-header {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 20px 40px -12px rgba(0, 0, 0, 0.08),
        0 8px 16px -8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-header:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 12px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, 
        rgba(239, 68, 68, 0.8) 0%, 
        rgba(245, 158, 11, 0.8) 35%, 
        rgba(59, 130, 246, 0.8) 70%, 
        rgba(139, 92, 246, 0.8) 100%);
    border-radius: 16px 16px 0 0;
}

.page-header::after {
    content: '';
    position: absolute;
    top: 6px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.8) 0%, 
        rgba(255, 255, 255, 0.2) 100%);
}

.page-title {
    font-size: 1.6rem;
    font-weight: 800;
    margin-bottom: 8px;
    background: linear-gradient(135deg, 
        rgba(239, 68, 68, 0.9) 0%, 
        rgba(245, 158, 11, 0.9) 50%, 
        rgba(59, 130, 246, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.page-description {
    color: rgba(100, 116, 139, 0.8);
    margin: 0;
    font-size: 0.95rem;
    font-weight: 500;
    line-height: 1.5;
}

.alert-indicator {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(239, 68, 68, 0.9);
    font-weight: 700;
    font-size: 0.8rem;
    background: rgba(254, 242, 242, 0.9);
    backdrop-filter: blur(10px);
    padding: 8px 14px;
    border-radius: 14px;
    border: 1px solid rgba(254, 226, 226, 0.5);
    box-shadow: 
        0 8px 16px -4px rgba(239, 68, 68, 0.1),
        0 4px 8px -4px rgba(239, 68, 68, 0.06);
}

.pulse-dot {
    width: 10px;
    height: 10px;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
    border-radius: 50%;
    animation: pulse-apple 2s infinite;
    box-shadow: 
        0 0 0 0 rgba(239, 68, 68, 0.4),
        0 2px 4px rgba(239, 68, 68, 0.2);
}

@keyframes pulse-apple {
    0% { 
        opacity: 1; 
        transform: scale(1);
        box-shadow: 
            0 0 0 0 rgba(239, 68, 68, 0.4),
            0 2px 4px rgba(239, 68, 68, 0.2);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1);
        box-shadow: 
            0 0 0 8px rgba(239, 68, 68, 0.1),
            0 2px 8px rgba(239, 68, 68, 0.3);
    }
    100% { 
        opacity: 1; 
        transform: scale(1);
        box-shadow: 
            0 0 0 0 rgba(239, 68, 68, 0.4),
            0 2px 4px rgba(239, 68, 68, 0.2);
    }
}

/* 风险统计卡片网格 */
.risk-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.risk-stat-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 8px 24px -4px rgba(0, 0, 0, 0.08),
        0 4px 12px -4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.risk-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: 16px 16px 0 0;
}

.risk-stat-card.critical::before {
    background: linear-gradient(90deg, 
        rgba(239, 68, 68, 0.8) 0%, 
        rgba(220, 38, 38, 0.9) 100%);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.risk-stat-card.high::before {
    background: linear-gradient(90deg, 
        rgba(245, 158, 11, 0.8) 0%, 
        rgba(217, 119, 6, 0.9) 100%);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.risk-stat-card.medium::before {
    background: linear-gradient(90deg, 
        rgba(59, 130, 246, 0.8) 0%, 
        rgba(37, 99, 235, 0.9) 100%);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.risk-stat-card.low::before {
    background: linear-gradient(90deg, 
        rgba(16, 185, 129, 0.8) 0%, 
        rgba(5, 150, 105, 0.9) 100%);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.risk-stat-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 
        0 20px 40px -8px rgba(0, 0, 0, 0.15),
        0 8px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.risk-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;
}

.risk-title {
    font-size: 0.8rem;
    color: rgba(100, 116, 139, 0.8);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.6px;
}

.risk-icon {
    width: 44px;
    height: 44px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.4rem;
    box-shadow: 
        0 8px 16px -4px rgba(0, 0, 0, 0.15),
        0 4px 8px -4px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.risk-icon.critical {
    background: linear-gradient(135deg, 
        rgba(239, 68, 68, 0.9) 0%, 
        rgba(220, 38, 38, 1) 100%);
}

.risk-icon.high {
    background: linear-gradient(135deg, 
        rgba(245, 158, 11, 0.9) 0%, 
        rgba(217, 119, 6, 1) 100%);
}

.risk-icon.medium {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.9) 0%, 
        rgba(37, 99, 235, 1) 100%);
}

.risk-icon.low {
    background: linear-gradient(135deg, 
        rgba(16, 185, 129, 0.9) 0%, 
        rgba(5, 150, 105, 1) 100%);
}

.risk-stat-card:hover .risk-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 
        0 12px 24px -4px rgba(0, 0, 0, 0.2),
        0 8px 16px -4px rgba(0, 0, 0, 0.12);
}

.risk-value {
    font-size: 2.2rem;
    font-weight: 800;
    color: rgba(15, 23, 42, 0.9);
    margin-bottom: 12px;
    line-height: 1;
    letter-spacing: -0.02em;
}

.risk-change {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.risk-change.increase {
    color: rgba(239, 68, 68, 0.9);
    background: rgba(254, 242, 242, 0.8);
}

.risk-change.decrease {
    color: rgba(16, 185, 129, 0.9);
    background: rgba(236, 253, 245, 0.8);
}

.risk-change.stable {
    color: rgba(100, 116, 139, 0.8);
    background: rgba(248, 250, 252, 0.8);
}

/* 控制面板 */
.control-panel {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 
        0 8px 24px -4px rgba(0, 0, 0, 0.08),
        0 4px 12px -4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.control-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 16px;
}

.control-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: rgba(15, 23, 42, 0.9);
    margin: 0;
    letter-spacing: -0.02em;
}

.control-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    min-width: 280px;
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 38px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(15, 23, 42, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.search-input:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.12),
        0 4px 8px -2px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(100, 116, 139, 0.6);
    font-size: 1rem;
}

.filter-select {
    padding: 10px 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    font-size: 0.85rem;
    font-weight: 500;
    color: rgba(15, 23, 42, 0.9);
    cursor: pointer;
    min-width: 120px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.filter-select:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.12),
        0 4px 8px -2px rgba(59, 130, 246, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.refresh-btn {
    padding: 10px 16px;
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.9) 0%, 
        rgba(37, 99, 235, 1) 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.3),
        0 2px 4px -2px rgba(59, 130, 246, 0.2);
}

.refresh-btn:hover {
    background: linear-gradient(135deg, 
        rgba(37, 99, 235, 0.9) 0%, 
        rgba(29, 78, 216, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 
        0 8px 16px -4px rgba(59, 130, 246, 0.4),
        0 4px 8px -4px rgba(59, 130, 246, 0.3);
}

/* 风险等级标签页 */
.risk-tabs {
    display: flex;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    padding: 4px;
    border-radius: 14px;
    margin-bottom: 20px;
    overflow-x: auto;
    box-shadow: 
        0 2px 8px -2px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.5);
}

.risk-tab {
    padding: 8px 16px;
    background: transparent;
    border: none;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 10px;
    color: rgba(100, 116, 139, 0.8);
    margin: 2px;
}

.risk-tab.active {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    color: rgba(59, 130, 246, 0.9);
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.15),
        0 2px 4px -1px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
}

.risk-tab:not(.active):hover {
    background: rgba(255, 255, 255, 0.6);
    color: rgba(15, 23, 42, 0.8);
    transform: translateY(-1px);
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.tab-badge {
    background: linear-gradient(135deg, 
        rgba(239, 68, 68, 0.9) 0%, 
        rgba(220, 38, 38, 1) 100%);
    color: white;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 24px;
    text-align: center;
    line-height: 1;
    box-shadow: 
        0 2px 4px -1px rgba(239, 68, 68, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tab-badge.high {
    background: linear-gradient(135deg, 
        rgba(245, 158, 11, 0.9) 0%, 
        rgba(217, 119, 6, 1) 100%);
    box-shadow: 
        0 2px 4px -1px rgba(245, 158, 11, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tab-badge.medium {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.9) 0%, 
        rgba(37, 99, 235, 1) 100%);
    box-shadow: 
        0 2px 4px -1px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tab-badge.low {
    background: linear-gradient(135deg, 
        rgba(16, 185, 129, 0.9) 0%, 
        rgba(5, 150, 105, 1) 100%);
    box-shadow: 
        0 2px 4px -1px rgba(16, 185, 129, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 用户列表 */
.users-list {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 
        0 8px 24px -4px rgba(0, 0, 0, 0.08),
        0 4px 12px -4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.list-header {
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(15px);
    padding: 16px 20px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.select-all-checkbox {
    width: 18px;
    height: 18px;
    accent-color: rgba(59, 130, 246, 0.9);
    border-radius: 4px;
}

.bulk-action-btn {
    padding: 8px 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 4px;
}

.bulk-action-btn:hover {
    background: rgba(248, 250, 252, 0.9);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px -2px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.bulk-action-btn.primary {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.9) 0%, 
        rgba(37, 99, 235, 1) 100%);
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.3),
        0 2px 4px -2px rgba(59, 130, 246, 0.2);
}

.bulk-action-btn.primary:hover {
    background: linear-gradient(135deg, 
        rgba(37, 99, 235, 0.9) 0%, 
        rgba(29, 78, 216, 1) 100%);
    box-shadow: 
        0 8px 16px -4px rgba(59, 130, 246, 0.4),
        0 4px 8px -4px rgba(59, 130, 246, 0.3);
}

.bulk-action-btn.warning {
    background: linear-gradient(135deg, 
        rgba(245, 158, 11, 0.9) 0%, 
        rgba(217, 119, 6, 1) 100%);
    color: white;
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 
        0 4px 8px -2px rgba(245, 158, 11, 0.3),
        0 2px 4px -2px rgba(245, 158, 11, 0.2);
}

.bulk-action-btn.warning:hover {
    background: linear-gradient(135deg, 
        rgba(217, 119, 6, 0.9) 0%, 
        rgba(180, 83, 9, 1) 100%);
    box-shadow: 
        0 8px 16px -4px rgba(245, 158, 11, 0.4),
        0 4px 8px -4px rgba(245, 158, 11, 0.3);
}

.list-info {
    font-size: 0.8rem;
    font-weight: 500;
    color: rgba(100, 116, 139, 0.8);
}

.user-item {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-item:hover {
    background: rgba(248, 250, 252, 0.5);
    backdrop-filter: blur(10px);
}

.user-item:last-child {
    border-bottom: none;
}

.user-row {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* 已移除复选框样式 */

.user-avatar {
    width: 44px;
    height: 44px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 
        0 4px 8px -2px rgba(0, 0, 0, 0.15),
        0 2px 4px -2px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-item:hover .user-avatar {
    transform: scale(1.05);
    box-shadow: 
        0 8px 16px -4px rgba(0, 0, 0, 0.2),
        0 4px 8px -4px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-main {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.user-name {
    font-weight: 700;
    color: rgba(15, 23, 42, 0.9);
    font-size: 0.95rem;
    letter-spacing: -0.01em;
}

.user-id {
    color: rgba(100, 116, 139, 0.8);
    font-size: 0.8rem;
    font-weight: 500;
    background: rgba(248, 250, 252, 0.8);
    padding: 3px 8px;
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.5);
}

.vip-badge {
    background: linear-gradient(135deg, 
        rgba(251, 191, 36, 0.9) 0%, 
        rgba(245, 158, 11, 1) 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 700;
    box-shadow: 
        0 2px 4px -1px rgba(245, 158, 11, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.user-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 0.8rem;
    color: rgba(100, 116, 139, 0.8);
    flex-wrap: wrap;
    margin-bottom: 8px;
}

.risk-factors {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.risk-tag {
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    color: white;
    box-shadow: 
        0 2px 4px -1px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.risk-tag.login {
    background: linear-gradient(135deg, 
        rgba(239, 68, 68, 0.9) 0%, 
        rgba(220, 38, 38, 1) 100%);
}

.risk-tag.recharge {
    background: linear-gradient(135deg, 
        rgba(245, 158, 11, 0.9) 0%, 
        rgba(217, 119, 6, 1) 100%);
}

.risk-tag.activity {
    background: linear-gradient(135deg, 
        rgba(139, 92, 246, 0.9) 0%, 
        rgba(124, 58, 237, 1) 100%);
}

.user-score {
    text-align: center;
    min-width: 70px;
    background: rgba(248, 250, 252, 0.6);
    padding: 12px;
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    backdrop-filter: blur(10px);
}

.score-value {
    font-size: 1.2rem;
    font-weight: 800;
    color: rgba(15, 23, 42, 0.9);
    margin-bottom: 4px;
    letter-spacing: -0.02em;
}

.score-label {
    font-size: 0.7rem;
    color: rgba(100, 116, 139, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.3px;
    font-weight: 600;
}

.user-actions {
    display: flex;
    gap: 6px;
}

.action-btn {
    padding: 6px 10px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: rgba(15, 23, 42, 0.9);
    font-size: 0.7rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn:hover {
    background: rgba(248, 250, 252, 0.9);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px -2px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-btn.primary {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.9) 0%, 
        rgba(37, 99, 235, 1) 100%);
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 
        0 2px 4px -1px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, 
        rgba(37, 99, 235, 0.9) 0%, 
        rgba(29, 78, 216, 1) 100%);
    box-shadow: 
        0 4px 8px -2px rgba(59, 130, 246, 0.4),
        0 2px 4px -2px rgba(59, 130, 246, 0.3);
}

.action-btn.success {
    background: linear-gradient(135deg, 
        rgba(16, 185, 129, 0.9) 0%, 
        rgba(5, 150, 105, 1) 100%);
    color: white;
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 
        0 2px 4px -1px rgba(16, 185, 129, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.action-btn.success:hover {
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.9) 0%, 
        rgba(4, 120, 87, 1) 100%);
    box-shadow: 
        0 4px 8px -2px rgba(16, 185, 129, 0.4),
        0 2px 4px -2px rgba(16, 185, 129, 0.3);
}

/* 分页控件 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-lg);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-lg);
}

.pagination-btn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--background);
    color: var(--foreground);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.pagination-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    margin: 0 var(--spacing-md);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--muted-foreground);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--foreground);
}

.empty-description {
    font-size: 0.875rem;
    margin-bottom: var(--spacing-lg);
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    background: var(--surface);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    box-shadow: var(--shadow-xl);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .risk-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .user-row {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    
    .user-actions {
        width: 100%;
        justify-content: flex-end;
        margin-top: var(--spacing-sm);
    }
}

@media (max-width: 768px) {
    .churn-warning-container {
        padding: var(--spacing-md);
    }
    
    .risk-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .control-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .control-actions {
        width: 100%;
        justify-content: stretch;
    }
    
    .search-box {
        min-width: auto;
        flex: 1;
    }
    
    .user-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .user-info {
        width: 100%;
    }
    
    .user-meta {
        flex-wrap: wrap;
    }
    
    .pagination {
        flex-wrap: wrap;
    }
    
    .alert-indicator {
        position: static;
        margin-top: var(--spacing-md);
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .risk-tabs {
        flex-direction: column;
        border-bottom: none;
        border-right: 2px solid var(--border);
    }
    
    .risk-tab {
        justify-content: flex-start;
        padding: var(--spacing-md);
    }
    
    .risk-tab.active::after {
        top: 0;
        bottom: 0;
        right: -2px;
        left: auto;
        width: 2px;
        height: auto;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="churn-warning-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">流失预警中心</h1>
        <p class="page-description">实时监控大R用户流失风险，及时采取挽留措施</p>
        <div class="alert-indicator">
            <div class="pulse-dot"></div>
            <span>发现 <strong id="criticalCount">12</strong> 位严重风险用户</span>
        </div>
    </div>

    <!-- 风险统计卡片 -->
    <div class="risk-stats-grid">
        <div class="risk-stat-card critical">
            <div class="risk-header">
                <div class="risk-title">严重风险用户</div>
                <div class="risk-icon critical">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                </div>
            </div>
            <div class="risk-value" id="criticalUsers">--</div>
            <div class="risk-change increase" id="criticalChange">
                <i class="bi bi-arrow-up"></i>
                <span>较昨日 +3</span>
            </div>
        </div>
        
        <div class="risk-stat-card high">
            <div class="risk-header">
                <div class="risk-title">高风险用户</div>
                <div class="risk-icon high">
                    <i class="bi bi-exclamation-circle-fill"></i>
                </div>
            </div>
            <div class="risk-value" id="highUsers">--</div>
            <div class="risk-change stable" id="highChange">
                <i class="bi bi-dash"></i>
                <span>较昨日 持平</span>
            </div>
        </div>
        
        <div class="risk-stat-card medium">
            <div class="risk-header">
                <div class="risk-title">中等风险用户</div>
                <div class="risk-icon medium">
                    <i class="bi bi-info-circle-fill"></i>
                </div>
            </div>
            <div class="risk-value" id="mediumUsers">--</div>
            <div class="risk-change decrease" id="mediumChange">
                <i class="bi bi-arrow-down"></i>
                <span>较昨日 -5</span>
            </div>
        </div>
        
        <div class="risk-stat-card low">
            <div class="risk-header">
                <div class="risk-title">低风险用户</div>
                <div class="risk-icon low">
                    <i class="bi bi-check-circle-fill"></i>
                </div>
            </div>
            <div class="risk-value" id="lowUsers">--</div>
            <div class="risk-change decrease" id="lowChange">
                <i class="bi bi-arrow-down"></i>
                <span>较昨日 -2</span>
            </div>
        </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
        <div class="control-header">
            <h2 class="control-title">用户管理面板</h2>
            <div class="control-actions">
                <div class="search-box">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索用户名、角色名或用户ID..." id="userSearch">
                </div>
                <select class="filter-select" id="riskLevelFilter">
                    <option value="">所有风险等级</option>
                    <option value="critical">严重风险</option>
                    <option value="high">高风险</option>
                    <option value="medium">中等风险</option>
                    <option value="low">低风险</option>
                </select>
                <select class="filter-select" id="serverFilter">
                    <option value="">所有服务器</option>
                    <option value="1">服务器1</option>
                    <option value="2">服务器2</option>
                    <option value="3">服务器3</option>
                </select>
                <button class="refresh-btn" id="refreshBtn">
                    <i class="bi bi-arrow-clockwise"></i>
                    刷新
                </button>
            </div>
        </div>

        <!-- 风险等级标签页 -->
        <div class="risk-tabs">
            <button class="risk-tab active" data-risk="all">
                <i class="bi bi-list-ul"></i>
                全部用户
                <span class="tab-badge" id="allCount">156</span>
            </button>
            <button class="risk-tab" data-risk="critical">
                <i class="bi bi-exclamation-triangle-fill"></i>
                严重风险
                <span class="tab-badge critical" id="criticalTabCount">12</span>
            </button>
            <button class="risk-tab" data-risk="high">
                <i class="bi bi-exclamation-circle-fill"></i>
                高风险
                <span class="tab-badge high" id="highTabCount">28</span>
            </button>
            <button class="risk-tab" data-risk="medium">
                <i class="bi bi-info-circle-fill"></i>
                中等风险
                <span class="tab-badge medium" id="mediumTabCount">67</span>
            </button>
            <button class="risk-tab" data-risk="low">
                <i class="bi bi-check-circle-fill"></i>
                低风险
                <span class="tab-badge low" id="lowTabCount">49</span>
            </button>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="users-list">
        <div class="list-header">
            <div class="list-info">
                显示 <span id="currentStart">1</span>-<span id="currentEnd">20</span> 条，共 <span id="totalUsers">156</span> 条记录
            </div>
        </div>
        
        <div id="usersList">
            <!-- 动态加载的用户列表 -->
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="bi bi-people"></i>
                </div>
                <div class="empty-title">正在加载用户数据</div>
                <div class="empty-description">请稍候，我们正在为您获取最新的用户风险信息...</div>
            </div>
        </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination" id="pagination">
        <button class="pagination-btn" id="prevBtn" disabled>
            <i class="bi bi-chevron-left"></i>
        </button>
        <div class="pagination-info">
            第 <span id="currentPage">1</span> 页，共 <span id="totalPages">8</span> 页
        </div>
        <button class="pagination-btn" id="nextBtn">
            <i class="bi bi-chevron-right"></i>
        </button>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <span>处理中，请稍候...</span>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 流失预警中心JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 流失预警中心页面加载');

    // 页面状态
    let currentRiskLevel = 'all';
    let currentPage = 1;
    let pageSize = 20;
    let searchQuery = '';
    let serverFilter = '';
    // 已移除批量选择功能

    // 初始化页面
    initializePage();

    // 显示通知
    function showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="bi bi-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="bi bi-x"></i>
            </button>
        `;
        
        // 添加通知样式
        if (!document.getElementById('notificationStyles')) {
            const styles = document.createElement('style');
            styles.id = 'notificationStyles';
            styles.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    padding: 16px;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    z-index: 10000;
                    min-width: 300px;
                    animation: slideIn 0.3s ease;
                    border-left: 4px solid;
                }
                
                .notification-success { border-left-color: #10b981; }
                .notification-error { border-left-color: #ef4444; }
                .notification-warning { border-left-color: #f59e0b; }
                .notification-info { border-left-color: #3b82f6; }
                
                .notification-content {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    flex: 1;
                }
                
                .notification-close {
                    background: none;
                    border: none;
                    cursor: pointer;
                    color: #6b7280;
                    padding: 4px;
                    border-radius: 4px;
                }
                
                .notification-close:hover {
                    background: #f3f4f6;
                }
                
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }
        
        // 显示通知
        document.body.appendChild(notification);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
    
    function getNotificationIcon(type) {
        const icons = {
            success: 'check-circle-fill',
            error: 'exclamation-circle-fill',
            warning: 'exclamation-triangle-fill',
            info: 'info-circle-fill'
        };
        return icons[type] || 'info-circle-fill';
    }

    function initializePage() {
        // 加载风险统计数据
        loadRiskStats();
        
        // 加载用户列表
        loadUsersList();
        
        // 绑定事件监听器
        bindEventListeners();
        
        console.log('✅ 流失预警中心初始化完成');
    }

    // 绑定事件监听器
    function bindEventListeners() {
        // 风险等级标签页切换
        document.querySelectorAll('.risk-tab').forEach(tab => {
            tab.addEventListener('click', () => switchRiskTab(tab.dataset.risk));
        });

        // 搜索功能
        const searchInput = document.getElementById('userSearch');
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchQuery = e.target.value.trim();
                currentPage = 1;
                loadUsersList();
            }, 500);
        });

        // 筛选器
        document.getElementById('riskLevelFilter').addEventListener('change', (e) => {
            if (e.target.value) {
                switchRiskTab(e.target.value);
            }
        });

        document.getElementById('serverFilter').addEventListener('change', (e) => {
            serverFilter = e.target.value;
            currentPage = 1;
            loadUsersList();
        });

        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            refreshData();
        });

        // 注意：已移除批量操作功能

        // 分页控件
        document.getElementById('prevBtn').addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                loadUsersList();
            }
        });

        document.getElementById('nextBtn').addEventListener('click', () => {
            currentPage++;
            loadUsersList();
        });
    }

    // 切换风险等级标签页
    function switchRiskTab(riskLevel) {
        // 更新标签页状态
        document.querySelectorAll('.risk-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-risk="${riskLevel}"]`).classList.add('active');

        // 更新筛选器
        document.getElementById('riskLevelFilter').value = riskLevel === 'all' ? '' : riskLevel;

        // 更新状态
        currentRiskLevel = riskLevel;
        currentPage = 1;
        // 已移除选择状态清理

        // 重新加载用户列表
        loadUsersList();
    }

    // 加载风险统计数据
    async function loadRiskStats() {
        try {
            const response = await fetch('/churn-warning/api/stats/');
            const result = await response.json();

            if (result.success) {
                // 更新统计卡片
                updateRiskStats(result.data);
                console.log('✅ 风险统计数据加载成功');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('❌ 风险统计数据加载失败:', error);
            showNotification('风险统计数据加载失败，请稍后重试', 'error');
            
            // 使用模拟数据作为后备
            const stats = {
                critical: { value: 12, change: '+3' },
                high: { value: 28, change: '0' },
                medium: { value: 67, change: '-5' },
                low: { value: 49, change: '-2' }
            };
            updateRiskStats(stats);
        }
    }

    // 更新风险统计显示
    function updateRiskStats(stats) {
        Object.entries(stats).forEach(([level, data]) => {
            const valueElement = document.getElementById(`${level}Users`);
            const changeElement = document.getElementById(`${level}Change`);
            
            if (valueElement) {
                animateValue(valueElement, 0, data.value, 1000);
            }
            
            if (changeElement) {
                const span = changeElement.querySelector('span');
                if (span) {
                    span.textContent = `较昨日 ${data.change}`;
                }
                
                // 更新变化指标样式
                if (data.change.startsWith('+')) {
                    changeElement.className = 'risk-change increase';
                    changeElement.querySelector('i').className = 'bi bi-arrow-up';
                } else if (data.change.startsWith('-')) {
                    changeElement.className = 'risk-change decrease';
                    changeElement.querySelector('i').className = 'bi bi-arrow-down';
                } else {
                    changeElement.className = 'risk-change stable';
                    changeElement.querySelector('i').className = 'bi bi-dash';
                }
            }
        });

        // 更新标签页计数
        document.getElementById('criticalTabCount').textContent = stats.critical.value;
        document.getElementById('highTabCount').textContent = stats.high.value;
        document.getElementById('mediumTabCount').textContent = stats.medium.value;
        document.getElementById('lowTabCount').textContent = stats.low.value;
        
        const totalCount = stats.critical.value + stats.high.value + stats.medium.value + stats.low.value;
        document.getElementById('allCount').textContent = totalCount;
        document.getElementById('criticalCount').textContent = stats.critical.value;
    }

    // 数值动画
    function animateValue(element, start, end, duration) {
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }

    // 加载用户列表
    async function loadUsersList() {
        try {
            showLoading(true);
            
            // 构建查询参数
            const params = new URLSearchParams({
                page: currentPage,
                page_size: pageSize,
                risk_level: currentRiskLevel === 'all' ? '' : currentRiskLevel,
                search: searchQuery,
                server: serverFilter
            });

            const response = await fetch(`/churn-warning/api/users/?${params}`);
            const result = await response.json();

            if (result.success) {
                // 渲染用户列表
                renderUsersList(result.data.users);
                
                // 更新分页信息
                updatePaginationInfo(result.data.pagination);
                
                console.log('✅ 用户列表加载成功');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('❌ 用户列表加载失败:', error);
            showNotification('用户列表加载失败，请稍后重试', 'error');
            
            // 使用模拟数据作为后备
            const mockData = generateMockUsers();
            renderUsersList(mockData.users);
            updatePaginationInfo(mockData.pagination);
        } finally {
            showLoading(false);
        }
    }

    // 生成模拟用户数据
    function generateMockUsers() {
        const riskLevels = ['critical', 'high', 'medium', 'low'];
        const names = ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十'];
        const servers = ['服务器1', '服务器2', '服务器3'];
        const riskTags = [
            { type: 'login', text: '登录风险' },
            { type: 'recharge', text: '充值风险' },
            { type: 'activity', text: '活跃风险' }
        ];

        const users = [];
        for (let i = 0; i < 20; i++) {
            const riskLevel = currentRiskLevel === 'all' ? riskLevels[Math.floor(Math.random() * 4)] : currentRiskLevel;
            const name = names[Math.floor(Math.random() * names.length)];
            const userId = 10001 + i;
            
            const vipLevel = Math.floor(Math.random() * 10) + 1;
            users.push({
                id: userId,
                username: `user_${userId}`,
                character_name: `${name}的角色`,
                server_name: servers[Math.floor(Math.random() * 3)],
                vip_level: vipLevel,
                vip_level_name: `VIP${vipLevel}`, // 模拟数据使用传统格式
                risk_level: riskLevel,
                risk_score: Math.floor(Math.random() * 100),
                total_recharge: Math.floor(Math.random() * 100000),
                last_login: `${Math.floor(Math.random() * 10) + 1}天前`,
                last_recharge: `${Math.floor(Math.random() * 30) + 1}天前`,
                risk_factors: riskTags.slice(0, Math.floor(Math.random() * 3) + 1)
            });
        }

        return {
            users: users,
            pagination: {
                current_page: currentPage,
                total_pages: 8,
                total_count: 156,
                page_size: pageSize
            }
        };
    }

    // 渲染用户列表
    function renderUsersList(users) {
        const container = document.getElementById('usersList');
        
        if (users.length === 0) {
            showEmptyState('暂无符合条件的用户');
            return;
        }

        const usersHTML = users.map(user => `
            <div class="user-item">
                <div class="user-row">
                    <!-- 已移除复选框 -->
                    <div class="user-avatar" style="background: ${getUserAvatarColor(user.risk_level)}">
                        ${user.character_name.charAt(0)}
                    </div>
                    <div class="user-info">
                        <div class="user-main">
                            <span class="user-name">${user.character_name}</span>
                            <span class="user-id">ID: ${user.id}</span>
                            <span class="vip-badge">${user.vip_level_name}</span>
                        </div>
                        <div class="user-meta">
                            <span><i class="bi bi-server"></i> ${user.server_name}</span>
                            <span><i class="bi bi-currency-dollar"></i> ${user.total_recharge.toLocaleString()}元</span>
                            <span><i class="bi bi-clock"></i> 上次登录: ${user.last_login}</span>
                            <span><i class="bi bi-credit-card"></i> 上次充值: ${user.last_recharge}</span>
                        </div>
                        <div class="risk-factors">
                            ${user.risk_factors.map(factor => 
                                `<span class="risk-tag ${factor.type}">${factor.text}</span>`
                            ).join('')}
                        </div>
                    </div>
                    <div class="user-score">
                        <div class="score-value" style="color: ${getRiskColor(user.risk_level)}">
                            ${user.risk_score}
                        </div>
                        <div class="score-label">风险评分</div>
                    </div>
                    <div class="user-actions">
                        <button class="action-btn primary" onclick="contactUser(${user.id})">
                            <i class="bi bi-chat-dots"></i>
                            联系
                        </button>
                        <button class="action-btn success" onclick="sendGift(${user.id})">
                            <i class="bi bi-gift"></i>
                            赠送
                        </button>
                        <button class="action-btn" onclick="viewDetails(${user.id})">
                            <i class="bi bi-eye"></i>
                            详情
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = usersHTML;

        // 已移除复选框绑定
    }

    // 获取用户头像颜色
    function getUserAvatarColor(riskLevel) {
        const colors = {
            critical: '#ef4444',
            high: '#f59e0b',
            medium: '#3b82f6',
            low: '#10b981'
        };
        return colors[riskLevel] || '#6b7280';
    }

    // 获取风险等级颜色
    function getRiskColor(riskLevel) {
        const colors = {
            critical: '#ef4444',
            high: '#f59e0b',
            medium: '#3b82f6',
            low: '#10b981'
        };
        return colors[riskLevel] || '#6b7280';
    }

    // 显示空状态
    function showEmptyState(message) {
        const container = document.getElementById('usersList');
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="bi bi-people"></i>
                </div>
                <div class="empty-title">暂无数据</div>
                <div class="empty-description">${message}</div>
            </div>
        `;
    }

    // 更新分页信息
    function updatePaginationInfo(pagination) {
        document.getElementById('currentPage').textContent = pagination.current_page;
        document.getElementById('totalPages').textContent = pagination.total_pages;
        document.getElementById('totalUsers').textContent = pagination.total_count;
        
        const start = (pagination.current_page - 1) * pagination.page_size + 1;
        const end = Math.min(pagination.current_page * pagination.page_size, pagination.total_count);
        document.getElementById('currentStart').textContent = start;
        document.getElementById('currentEnd').textContent = end;

        // 更新分页按钮状态
        document.getElementById('prevBtn').disabled = pagination.current_page <= 1;
        document.getElementById('nextBtn').disabled = pagination.current_page >= pagination.total_pages;
    }

    // 已移除复选框相关功能

    // 已移除批量操作相关功能

    // 显示/隐藏加载状态
    function showLoading(show) {
        document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
    }

    // 刷新数据
    function refreshData() {
        // 已移除选择状态清理
        loadRiskStats();
        loadUsersList();
    }

    // 已移除批量操作功能

    // 全局函数 - 联系单个用户
    window.contactUser = async function(userId) {
        try {
            showLoading(true);
            
            const response = await fetch(`/churn-warning/api/users/${userId}/contact/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: '主动联系用户'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showNotification(result.message, 'success');
                loadUsersList(); // 重新加载用户列表
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('联系用户失败:', error);
            showNotification('联系用户失败，请稍后重试', 'error');
        } finally {
            showLoading(false);
        }
    };

    // 全局函数 - 赠送礼包
    window.sendGift = async function(userId) {
        try {
            showLoading(true);
            
            const response = await fetch(`/churn-warning/api/users/${userId}/gift/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    gift_type: 'default'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showNotification(result.message, 'success');
                loadUsersList(); // 重新加载用户列表
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('赠送礼包失败:', error);
            showNotification('赠送礼包失败，请稍后重试', 'error');
        } finally {
            showLoading(false);
        }
    };

    // 全局函数 - 查看详情
    window.viewDetails = function(userId) {
        // 跳转到用户详情页面
        window.location.href = `/churn-warning/user/${userId}/`;
    };
});
</script>
{% endblock %}