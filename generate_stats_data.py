#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成统计数据脚本
"""

import os
import django
import random
from datetime import datetime, timedelta, date
from decimal import Decimal
from django.utils import timezone

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'VIPManager.settings')
django.setup()

from big_r_overview.models import *
from churn_warning.models import *
from privileges.models import *
from service_analysis.models import *

print("开始生成统计数据...")

# 获取现有数据
users = list(BigRUser.objects.all())
privileges = list(Privilege.objects.all())
predictions = list(ChurnPrediction.objects.all())

print(f"用户数: {len(users)}")
print(f"特权数: {len(privileges)}")
print(f"预测数: {len(predictions)}")

# 1. 跳过特权使用记录补充，直接使用现有数据
print("1. 使用现有特权使用记录...")
print(f"特权使用记录: {PrivilegeUsageLog.objects.count()} 条")

# 2. 生成特权统计数据
print("2. 生成特权统计数据...")
for privilege in privileges:
    if PrivilegeStats.objects.filter(privilege=privilege).exists():
        continue
        
    # 从使用记录计算统计数据
    usage_logs = PrivilegeUsageLog.objects.filter(privilege=privilege)
    
    total_users = usage_logs.values('user_id').distinct().count()
    total_usage = sum(log.usage_count for log in usage_logs)
    
    # 计算不同时间段的活跃用户数
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    active_users_today = usage_logs.filter(usage_date=today).values('user_id').distinct().count()
    active_users_week = usage_logs.filter(usage_date__gte=week_ago).values('user_id').distinct().count()
    active_users_month = usage_logs.filter(usage_date__gte=month_ago).values('user_id').distinct().count()
    
    # 计算人均使用次数
    avg_usage_per_user = total_usage / max(1, total_users)
    
    # 计算受欢迎度评分
    if total_users > 0:
        active_ratio = active_users_week / total_users if total_users > 0 else 0
        popularity_score = min(10.0, active_ratio * avg_usage_per_user * 2)
    else:
        popularity_score = 0.0
    
    PrivilegeStats.objects.create(
        privilege=privilege,
        total_users=total_users,
        active_users_today=active_users_today,
        active_users_week=active_users_week,
        active_users_month=active_users_month,
        total_usage=total_usage,
        avg_usage_per_user=avg_usage_per_user,
        popularity_score=popularity_score
    )

print(f"特权统计数据: {PrivilegeStats.objects.count()} 条")

# 3. 生成大R总览统计数据
print("3. 生成大R总览统计数据...")
if BigROverviewStats.objects.count() == 0:
    for i in range(30):  # 生成最近30天的统计数据
        stat_date = timezone.now().date() - timedelta(days=i)
        
        # 计算当日的各种指标
        base_users = len(users)
        daily_variation = random.randint(-2, 3)  # 每日变化
        total_big_r_users = max(0, base_users + daily_variation)
        
        # 统计潜力用户和风险用户
        total_potential_users = len([u for u in users if u.is_potential])
        total_churn_warning_users = len([u for u in users if u.is_churn_risk])
        
        # 计算收入相关指标
        total_revenue = sum(u.total_recharge for u in users)
        avg_arpu = total_revenue / max(1, total_big_r_users)
        
        # VIP分布统计
        vip_distribution = {}
        for level in range(6):  # 0-5级
            vip_distribution[f"vip_{level}"] = len([u for u in users if u.vip_level == level])
        
        # 新增和流失用户（模拟数据）
        new_big_r_users = random.randint(0, 2) if i < 7 else 0  # 最近一周可能有新用户
        lost_big_r_users = random.randint(0, 1)
        
        BigROverviewStats.objects.create(
            stat_date=stat_date,
            total_big_r_users=total_big_r_users,
            total_potential_users=total_potential_users,
            total_churn_warning_users=total_churn_warning_users,
            total_revenue=total_revenue,
            avg_arpu=avg_arpu,
            vip_distribution=vip_distribution,
            new_big_r_users=new_big_r_users,
            lost_big_r_users=lost_big_r_users
        )

print(f"大R总览统计: {BigROverviewStats.objects.count()} 条")

# 4. 生成流失预警统计数据
print("4. 生成流失预警统计数据...")
if ChurnWarningStats.objects.count() == 0:
    for i in range(30):  # 生成最近30天的统计数据
        stat_date = timezone.now().date() - timedelta(days=i)
        
        # 按风险等级统计用户数量（添加随机波动）
        base_critical = len([p for p in predictions if p.risk_level == 'critical'])
        base_high = len([p for p in predictions if p.risk_level == 'high'])
        base_medium = len([p for p in predictions if p.risk_level == 'medium'])
        base_low = len([p for p in predictions if p.risk_level == 'low'])
        
        # 处理状态统计
        contacted_users = len([p for p in predictions if p.status in ['contacted', 'intervened', 'recovered']])
        intervened_users = len([p for p in predictions if p.status in ['intervened', 'recovered']])
        recovered_users = len([p for p in predictions if p.status == 'recovered'])
        churned_users = len([p for p in predictions if p.status == 'churned'])
        
        # 计算干预成功率
        if intervened_users > 0:
            intervention_success_rate = (recovered_users / intervened_users) * 100
        else:
            intervention_success_rate = 0.0
        
        # 计算平均风险评分
        if predictions:
            avg_risk_score = sum(p.risk_score for p in predictions) / len(predictions)
        else:
            avg_risk_score = 0.0
        
        # 生成变化趋势（相对前一天的变化）
        ChurnWarningStats.objects.create(
            stat_date=stat_date,
            critical_users=max(0, base_critical + random.randint(-1, 1)),
            high_risk_users=max(0, base_high + random.randint(-1, 2)),
            medium_risk_users=max(0, base_medium + random.randint(-2, 2)),
            low_risk_users=max(0, base_low + random.randint(-2, 3)),
            contacted_users=max(0, contacted_users + random.randint(-1, 1)),
            intervened_users=max(0, intervened_users + random.randint(0, 1)),
            recovered_users=max(0, recovered_users + random.randint(0, 1)),
            churned_users=max(0, churned_users + random.randint(0, 1)),
            intervention_success_rate=max(0, min(100, intervention_success_rate + random.randint(-5, 5))),
            avg_risk_score=max(0, min(100, avg_risk_score + random.randint(-3, 3))),
            critical_change=random.randint(-1, 1),
            high_risk_change=random.randint(-2, 2),
            medium_risk_change=random.randint(-2, 2),
            low_risk_change=random.randint(-3, 3)
        )

print(f"流失预警统计: {ChurnWarningStats.objects.count()} 条")

print("\n=== 统计数据生成完成！ ===")
print("最终数据统计:")
print(f"BigRUser: {BigRUser.objects.count()}")
print(f"UserRechargeRecord: {UserRechargeRecord.objects.count()}")
print(f"UserLoginRecord: {UserLoginRecord.objects.count()}")
print(f"UserBehaviorAnalysis: {UserBehaviorAnalysis.objects.count()}")
print(f"BigROverviewStats: {BigROverviewStats.objects.count()}")
print()
print(f"ChurnPrediction: {ChurnPrediction.objects.count()}")
print(f"ChurnInterventionLog: {ChurnInterventionLog.objects.count()}")
print(f"ChurnWarningStats: {ChurnWarningStats.objects.count()}")
print()
print(f"PrivilegeProcessRecord: {PrivilegeProcessRecord.objects.count()}")
print(f"PrivilegeUsageLog: {PrivilegeUsageLog.objects.count()}")
print(f"PrivilegeStats: {PrivilegeStats.objects.count()}")
print()
print("数据库现在包含完整的测试数据！")

# 5. 显示一些关键统计信息
print("\n=== 关键数据统计 ===")
print(f"高风险用户: {ChurnPrediction.objects.filter(risk_level='critical').count()} 人")
print(f"中高风险用户: {ChurnPrediction.objects.filter(risk_level='high').count()} 人")
print(f"已干预用户: {ChurnPrediction.objects.filter(status='intervened').count()} 人")
print(f"已联系用户: {ChurnPrediction.objects.filter(status='contacted').count()} 人")
print()
print(f"VIP1-5用户: {len([u for u in users if u.vip_level >= 1])} 人")
print(f"高价值用户(充值>5000): {len([u for u in users if u.total_recharge > 5000])} 人")
print(f"潜力用户: {len([u for u in users if u.is_potential])} 人")