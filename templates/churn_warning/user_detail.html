{% extends 'base.html' %}
{% load static %}

{% block title %}用户详情 - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}流失预警中心 / 用户详情{% endblock %}

{% block extra_css %}
<style>
/* 苹果风格用户详情页面样式 */
.user-detail-container {
    padding: 20px;
    min-height: 100vh;
    margin: 0;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.9) 100%);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.user-detail-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.user-detail-container > * {
    position: relative;
    z-index: 1;
}

/* 用户基本信息卡片 */
.user-info-card {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow:
        0 20px 40px -12px rgba(0, 0, 0, 0.08),
        0 8px 16px -8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-info-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 12px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.user-info-header {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.9) 0%,
        rgba(59, 130, 246, 0.9) 50%,
        rgba(16, 185, 129, 0.9) 100%);
    color: white;
    padding: 20px;
    position: relative;
}

.user-info-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.8) 100%);
    border-radius: 16px 16px 0 0;
}

.user-header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: 800;
    border: 3px solid rgba(255, 255, 255, 0.4);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.user-main-info h1 {
    font-size: 1.75rem;
    font-weight: 800;
    margin-bottom: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.02em;
}

.user-main-info p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 500;
}

.user-status-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    font-size: 0.875rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.user-info-body {
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
}

.user-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-item:hover {
    transform: translateY(-1px);
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-label {
    font-size: 0.75rem;
    color: rgba(100, 116, 139, 0.8);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
}

.info-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: rgba(30, 41, 59, 0.9);
}

.vip-badge-large {
    background: linear-gradient(135deg,
        rgba(251, 191, 36, 0.9) 0%,
        rgba(245, 158, 11, 0.9) 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 800;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    box-shadow:
        0 4px 8px rgba(245, 158, 11, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 风险分析卡片 */
.risk-analysis-card {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    margin-bottom: 20px;
    box-shadow:
        0 20px 40px -12px rgba(0, 0, 0, 0.08),
        0 8px 16px -8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.risk-analysis-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 12px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.risk-analysis-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        rgba(239, 68, 68, 0.8) 0%,
        rgba(245, 158, 11, 0.8) 50%,
        rgba(16, 185, 129, 0.8) 100%);
    border-radius: 16px 16px 0 0;
}

.risk-analysis-header {
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    position: relative;
}

.risk-analysis-title {
    font-size: 1.25rem;
    font-weight: 700;
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.9) 0%,
        rgba(245, 158, 11, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    letter-spacing: -0.02em;
}

.risk-analysis-body {
    padding: 20px;
}

.risk-score-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.risk-score-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.risk-score-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
        rgba(139, 92, 246, 0.6) 0%,
        rgba(59, 130, 246, 0.6) 100%);
    border-radius: 16px 16px 0 0;
}

.risk-score-item:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.9);
    box-shadow:
        0 20px 40px -12px rgba(0, 0, 0, 0.15),
        0 8px 16px -8px rgba(0, 0, 0, 0.08);
}

.risk-score-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 16px auto;
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.risk-score-icon.overall {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.9) 0%,
        rgba(139, 92, 246, 0.9) 100%);
}

.risk-score-icon.login {
    background: linear-gradient(135deg,
        rgba(239, 68, 68, 0.9) 0%,
        rgba(220, 38, 38, 0.9) 100%);
}

.risk-score-icon.recharge {
    background: linear-gradient(135deg,
        rgba(245, 158, 11, 0.9) 0%,
        rgba(217, 119, 6, 0.9) 100%);
}

.risk-score-icon.activity {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.9) 0%,
        rgba(124, 58, 237, 0.9) 100%);
}

.risk-score-value {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 4px;
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.9) 0%,
        rgba(51, 65, 85, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

.risk-score-label {
    font-size: 0.875rem;
    color: rgba(100, 116, 139, 0.8);
    margin-bottom: 8px;
    font-weight: 600;
}

.risk-level-badge {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
}

.risk-level-badge.critical {
    background: var(--error);
}

.risk-level-badge.high {
    background: var(--warning);
}

.risk-level-badge.medium {
    background: var(--info);
}

.risk-level-badge.low {
    background: var(--success);
}

/* 风险因素和建议措施 */
.factors-actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.factors-section, .actions-section {
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.section-title {
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 16px;
    background: linear-gradient(135deg,
        rgba(30, 41, 59, 0.9) 0%,
        rgba(51, 65, 85, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 8px;
    letter-spacing: -0.02em;
}

.risk-factor-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background);
    border-radius: var(--radius);
    margin-bottom: var(--spacing-sm);
}

.factor-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.factor-icon.login {
    background: var(--error);
}

.factor-icon.recharge {
    background: var(--warning);
}

.factor-icon.activity {
    background: #8b5cf6;
}

.factor-info {
    flex: 1;
}

.factor-name {
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.factor-score {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.action-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background);
    border-radius: var(--radius);
    margin-bottom: var(--spacing-sm);
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.action-icon.high {
    background: var(--error);
}

.action-icon.medium {
    background: var(--warning);
}

.action-icon.low {
    background: var(--info);
}

.action-info {
    flex: 1;
}

.action-name {
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.action-reason {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

/* 干预记录卡片 */
.intervention-card {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    margin-bottom: 20px;
    box-shadow:
        0 20px 40px -12px rgba(0, 0, 0, 0.08),
        0 8px 16px -8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.intervention-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 12px 24px -8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.intervention-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        rgba(16, 185, 129, 0.8) 0%,
        rgba(59, 130, 246, 0.8) 50%,
        rgba(139, 92, 246, 0.8) 100%);
    border-radius: 16px 16px 0 0;
}

.intervention-header {
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    position: relative;
}

.intervention-title {
    font-size: 1.25rem;
    font-weight: 700;
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.9) 0%,
        rgba(59, 130, 246, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    letter-spacing: -0.02em;
}

.intervention-body {
    padding: 20px;
}

.intervention-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    margin-bottom: var(--spacing-sm);
    transition: var(--transition-fast);
}

.intervention-item:hover {
    background: var(--muted);
}

.intervention-type-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.intervention-type-icon.contact {
    background: var(--primary);
}

.intervention-type-icon.gift {
    background: var(--success);
}

.intervention-type-icon.discount {
    background: var(--warning);
}

.intervention-details {
    flex: 1;
}

.intervention-name {
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.intervention-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.status-badge {
    padding: 2px 8px;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

.status-badge.planned {
    background: var(--info);
}

.status-badge.completed {
    background: var(--success);
}

.status-badge.failed {
    background: var(--error);
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.action-button {
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button.primary {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.9) 0%,
        rgba(37, 99, 235, 0.9) 100%);
    color: white;
}

.action-button.primary:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 1) 0%,
        rgba(37, 99, 235, 1) 100%);
    box-shadow:
        0 8px 16px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-button.success {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.9) 0%,
        rgba(5, 150, 105, 0.9) 100%);
    color: white;
}

.action-button.success:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 1) 0%,
        rgba(5, 150, 105, 1) 100%);
    box-shadow:
        0 8px 16px rgba(16, 185, 129, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-button.secondary {
    background: rgba(255, 255, 255, 0.8);
    color: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.action-button.secondary:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--muted-foreground);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--foreground);
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    background: var(--surface);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    box-shadow: var(--shadow-xl);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-detail-container {
        padding: 16px;
    }

    .user-header-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .user-status-badge {
        position: static;
        margin-top: 16px;
        align-self: center;
    }

    .user-info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .risk-score-section {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .factors-actions-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .action-button {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .user-detail-container {
        padding: 12px;
    }

    .risk-score-section {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .user-info-header {
        padding: 16px;
    }

    .user-info-body {
        padding: 16px;
    }

    .risk-analysis-body {
        padding: 16px;
    }

    .intervention-body {
        padding: 16px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="user-detail-container">
    <!-- 返回按钮 -->
    <div class="action-buttons">
        <button class="action-button secondary" onclick="window.history.back()">
            <i class="bi bi-arrow-left"></i>
            返回列表
        </button>
        <button class="action-button primary" id="contactUserBtn">
            <i class="bi bi-chat-dots"></i>
            联系用户
        </button>
        <button class="action-button success" id="sendGiftBtn">
            <i class="bi bi-gift"></i>
            赠送礼包
        </button>
    </div>

    <!-- 用户基本信息 -->
    <div class="user-info-card">
        <div class="user-info-header">
            <div class="user-header-content">
                <div class="user-avatar-large" id="userAvatar">
                    --
                </div>
                <div class="user-main-info">
                    <h1 id="userName">加载中...</h1>
                    <p id="userSubInfo">正在获取用户信息</p>
                </div>
            </div>
            <div class="user-status-badge" id="userStatusBadge">
                <i class="bi bi-clock"></i>
                <span>加载中</span>
            </div>
        </div>
        <div class="user-info-body">
            <div class="user-info-grid">
                <div class="info-item">
                    <div class="info-label">用户ID</div>
                    <div class="info-value" id="userId">{{ user_id }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">所属服务器</div>
                    <div class="info-value" id="serverName">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">VIP等级</div>
                    <div class="info-value" id="vipLevel">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">累计充值</div>
                    <div class="info-value" id="totalRecharge">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">首次充值</div>
                    <div class="info-value" id="firstRecharge">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">最后登录</div>
                    <div class="info-value" id="lastLogin">--</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险分析 -->
    <div class="risk-analysis-card">
        <div class="risk-analysis-header">
            <h2 class="risk-analysis-title">
                <i class="bi bi-exclamation-triangle"></i>
                风险分析报告
            </h2>
        </div>
        <div class="risk-analysis-body">
            <!-- 风险评分 -->
            <div class="risk-score-section">
                <div class="risk-score-item">
                    <div class="risk-score-icon overall">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <div class="risk-score-value" id="overallRiskScore">--</div>
                    <div class="risk-score-label">综合风险评分</div>
                    <div class="risk-level-badge" id="riskLevelBadge">加载中</div>
                </div>
                <div class="risk-score-item">
                    <div class="risk-score-icon login">
                        <i class="bi bi-person-check"></i>
                    </div>
                    <div class="risk-score-value" id="loginRiskScore">--</div>
                    <div class="risk-score-label">登录风险评分</div>
                </div>
                <div class="risk-score-item">
                    <div class="risk-score-icon recharge">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <div class="risk-score-value" id="rechargeRiskScore">--</div>
                    <div class="risk-score-label">充值风险评分</div>
                </div>
                <div class="risk-score-item">
                    <div class="risk-score-icon activity">
                        <i class="bi bi-activity"></i>
                    </div>
                    <div class="risk-score-value" id="activityRiskScore">--</div>
                    <div class="risk-score-label">活跃风险评分</div>
                </div>
            </div>

            <!-- 风险因素和建议措施 -->
            <div class="factors-actions-grid">
                <div class="factors-section">
                    <h3 class="section-title">
                        <i class="bi bi-exclamation-circle"></i>
                        风险因素
                    </h3>
                    <div id="riskFactorsList">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="bi bi-hourglass-split"></i>
                            </div>
                            <div class="empty-title">加载中</div>
                        </div>
                    </div>
                </div>
                
                <div class="actions-section">
                    <h3 class="section-title">
                        <i class="bi bi-lightbulb"></i>
                        建议措施
                    </h3>
                    <div id="suggestedActionsList">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="bi bi-hourglass-split"></i>
                            </div>
                            <div class="empty-title">加载中</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 干预记录 -->
    <div class="intervention-card">
        <div class="intervention-header">
            <h2 class="intervention-title">
                <i class="bi bi-clipboard-check"></i>
                干预记录
            </h2>
        </div>
        <div class="intervention-body">
            <div id="interventionsList">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bi bi-hourglass-split"></i>
                    </div>
                    <div class="empty-title">加载中</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <span>处理中，请稍候...</span>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const userId = {{ user_id }};
    let userDetailData = null;

    // 初始化页面
    initializePage();

    function initializePage() {
        loadUserDetail();
        bindEventListeners();
    }

    function bindEventListeners() {
        // 联系用户按钮
        document.getElementById('contactUserBtn').addEventListener('click', () => {
            contactUser();
        });

        // 赠送礼包按钮
        document.getElementById('sendGiftBtn').addEventListener('click', () => {
            sendGift();
        });
    }

    // 加载用户详情数据
    async function loadUserDetail() {
        try {
            showLoading(true);
            
            const response = await fetch(`/churn-warning/api/users/${userId}/`);
            const result = await response.json();

            if (result.success) {
                userDetailData = result.data;
                renderUserDetail(result.data);
            } else {
                showError('加载用户详情失败：' + result.message);
            }
        } catch (error) {
            console.error('加载用户详情失败:', error);
            showError('加载用户详情失败，请稍后重试');
        } finally {
            showLoading(false);
        }
    }

    // 渲染用户详情
    function renderUserDetail(data) {
        // 基本信息
        document.getElementById('userAvatar').textContent = data.character_name.charAt(0);
        document.getElementById('userName').textContent = data.character_name;
        document.getElementById('userSubInfo').textContent = `${data.username} - ID: ${data.id}`;
        document.getElementById('serverName').textContent = data.server_name;
        
        const vipLevel = document.getElementById('vipLevel');
        vipLevel.innerHTML = `<div class="vip-badge-large"><i class="bi bi-gem"></i>${data.vip_level_name}</div>`;
        
        document.getElementById('totalRecharge').textContent = `¥${data.total_recharge.toLocaleString()}`;
        document.getElementById('firstRecharge').textContent = data.first_recharge_date || '未充值';
        document.getElementById('lastLogin').textContent = data.last_login_date || '从未登录';

        // 用户状态
        const statusBadge = document.getElementById('userStatusBadge');
        const statusMap = {
            'pending': { icon: 'clock', text: '待处理', color: 'var(--warning)' },
            'contacted': { icon: 'chat-dots', text: '已联系', color: 'var(--info)' },
            'intervened': { icon: 'check-circle', text: '已干预', color: 'var(--success)' },
            'recovered': { icon: 'shield-check', text: '已挽回', color: 'var(--success)' },
            'churned': { icon: 'x-circle', text: '已流失', color: 'var(--error)' }
        };
        const status = statusMap[data.status] || statusMap['pending'];
        statusBadge.innerHTML = `<i class="bi bi-${status.icon}"></i><span>${status.text}</span>`;
        statusBadge.style.borderColor = status.color;

        // 风险评分
        document.getElementById('overallRiskScore').textContent = Math.round(data.risk_score);
        document.getElementById('loginRiskScore').textContent = Math.round(data.login_risk_score);
        document.getElementById('rechargeRiskScore').textContent = Math.round(data.recharge_risk_score);
        document.getElementById('activityRiskScore').textContent = Math.round(data.activity_risk_score);

        // 风险等级
        const riskLevelBadge = document.getElementById('riskLevelBadge');
        const riskLevelMap = {
            'critical': '严重风险',
            'high': '高风险',
            'medium': '中等风险',
            'low': '低风险'
        };
        riskLevelBadge.textContent = riskLevelMap[data.risk_level] || '未知';
        riskLevelBadge.className = `risk-level-badge ${data.risk_level}`;

        // 预测流失日期
        if (data.predicted_churn_date) {
            document.getElementById('userSubInfo').textContent += ` - 预计流失: ${data.predicted_churn_date}`;
        }

        // 渲染风险因素
        renderRiskFactors(data.risk_factors || []);

        // 渲染建议措施
        renderSuggestedActions(data.suggested_actions || []);

        // 渲染干预记录
        renderInterventions(data.interventions || []);
    }

    // 渲染风险因素
    function renderRiskFactors(factors) {
        const container = document.getElementById('riskFactorsList');
        
        if (factors.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon"><i class="bi bi-shield-check"></i></div>
                    <div class="empty-title">暂无风险因素</div>
                </div>
            `;
            return;
        }

        const factorsHTML = factors.map(factor => {
            const iconMap = {
                'login': 'person-check',
                'recharge': 'credit-card',
                'activity': 'activity'
            };
            return `
                <div class="risk-factor-item">
                    <div class="factor-icon ${factor.type}">
                        <i class="bi bi-${iconMap[factor.type] || 'exclamation-triangle'}"></i>
                    </div>
                    <div class="factor-info">
                        <div class="factor-name">${factor.text}</div>
                        <div class="factor-score">风险评分: ${Math.round(factor.score || 0)}</div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = factorsHTML;
    }

    // 渲染建议措施
    function renderSuggestedActions(actions) {
        const container = document.getElementById('suggestedActionsList');
        
        if (actions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon"><i class="bi bi-lightbulb"></i></div>
                    <div class="empty-title">暂无建议措施</div>
                </div>
            `;
            return;
        }

        const actionsHTML = actions.map(action => {
            const iconMap = {
                'contact': 'chat-dots',
                'gift': 'gift',
                'discount': 'percent',
                'event': 'calendar-event',
                'service': 'headset',
                'monitor': 'eye',
                'maintenance': 'tools',
                'reward': 'award'
            };
            return `
                <div class="action-item">
                    <div class="action-icon ${action.priority}">
                        <i class="bi bi-${iconMap[action.type] || 'lightbulb'}"></i>
                    </div>
                    <div class="action-info">
                        <div class="action-name">${action.text}</div>
                        <div class="action-reason">${action.reason || '建议采取此措施'}</div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = actionsHTML;
    }

    // 渲染干预记录
    function renderInterventions(interventions) {
        const container = document.getElementById('interventionsList');
        
        if (interventions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon"><i class="bi bi-clipboard"></i></div>
                    <div class="empty-title">暂无干预记录</div>
                    <p>尚未对此用户采取任何干预措施</p>
                </div>
            `;
            return;
        }

        const interventionsHTML = interventions.map(intervention => {
            const iconMap = {
                'contact': 'chat-dots',
                'gift': 'gift',
                'discount': 'percent',
                'event': 'calendar-event',
                'service': 'headset'
            };
            return `
                <div class="intervention-item">
                    <div class="intervention-type-icon ${intervention.type}">
                        <i class="bi bi-${iconMap[intervention.type] || 'gear'}"></i>
                    </div>
                    <div class="intervention-details">
                        <div class="intervention-name">${intervention.type_display}</div>
                        <div class="intervention-meta">
                            <span><i class="bi bi-calendar"></i> ${intervention.created_at}</span>
                            <span><i class="bi bi-person"></i> ${intervention.executor}</span>
                            <span class="status-badge ${intervention.status}">${intervention.status_display}</span>
                            ${intervention.effectiveness_score ? `<span><i class="bi bi-bar-chart"></i> 效果: ${intervention.effectiveness_score}%</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = interventionsHTML;
    }

    // 联系用户
    async function contactUser() {
        try {
            showLoading(true);
            
            const response = await fetch(`/churn-warning/api/users/${userId}/contact/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: '从详情页面主动联系用户'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showNotification(result.message, 'success');
                // 重新加载数据
                loadUserDetail();
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('联系用户失败:', error);
            showNotification('联系用户失败，请稍后重试', 'error');
        } finally {
            showLoading(false);
        }
    }

    // 赠送礼包
    async function sendGift() {
        try {
            showLoading(true);
            
            const response = await fetch(`/churn-warning/api/users/${userId}/gift/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    gift_type: 'high_value'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showNotification(result.message, 'success');
                // 重新加载数据
                loadUserDetail();
            } else {
                showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('赠送礼包失败:', error);
            showNotification('赠送礼包失败，请稍后重试', 'error');
        } finally {
            showLoading(false);
        }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="bi bi-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="bi bi-x"></i>
            </button>
        `;
        
        // 添加通知样式（如果还没有的话）
        if (!document.getElementById('notificationStyles')) {
            const styles = document.createElement('style');
            styles.id = 'notificationStyles';
            styles.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    padding: 16px;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    z-index: 10000;
                    min-width: 300px;
                    animation: slideIn 0.3s ease;
                    border-left: 4px solid;
                }
                .notification-success { border-left-color: #10b981; }
                .notification-error { border-left-color: #ef4444; }
                .notification-warning { border-left-color: #f59e0b; }
                .notification-info { border-left-color: #3b82f6; }
                .notification-content {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    flex: 1;
                }
                .notification-close {
                    background: none;
                    border: none;
                    cursor: pointer;
                    color: #6b7280;
                    padding: 4px;
                    border-radius: 4px;
                }
                .notification-close:hover {
                    background: #f3f4f6;
                }
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }
        
        // 显示通知
        document.body.appendChild(notification);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
    
    function getNotificationIcon(type) {
        const icons = {
            success: 'check-circle-fill',
            error: 'exclamation-circle-fill',
            warning: 'exclamation-triangle-fill',
            info: 'info-circle-fill'
        };
        return icons[type] || 'info-circle-fill';
    }

    // 显示错误信息
    function showError(message) {
        document.body.innerHTML = `
            <div class="user-detail-container">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bi bi-exclamation-triangle text-danger"></i>
                    </div>
                    <div class="empty-title">加载失败</div>
                    <p>${message}</p>
                    <button class="action-button secondary" onclick="window.history.back()">
                        <i class="bi bi-arrow-left"></i>
                        返回列表
                    </button>
                </div>
            </div>
        `;
    }

    // 显示/隐藏加载状态
    function showLoading(show) {
        document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
    }
});
</script>
{% endblock %}