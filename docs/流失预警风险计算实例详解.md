# 流失预警中心 - 风险计算实例详解

## 📋 目录

1. [概述](#概述)
2. [用户角色设定](#用户角色设定)
3. [角色一：高风险流失用户](#角色一高风险流失用户)
4. [角色二：中等风险用户](#角色二中等风险用户)
5. [角色三：低风险稳定用户](#角色三低风险稳定用户)
6. [计算结果对比](#计算结果对比)
7. [风险因素分析](#风险因素分析)
8. [干预措施建议](#干预措施建议)

## 🎯 概述

本文档通过3个典型的大R用户角色案例，详细展示更新后的流失预警系统风险计算过程，包括登录风险、充值风险和新增的活跃风险三个维度。

### 计算基础参数

**风险因子权重配置**：
- 登录风险权重：30% (0.30)
- 充值风险权重：40% (0.40) - 最高权重
- 活跃风险权重：30% (0.30) - 包含在线时长和活跃度两个指标

**风险等级阈值**：
- 严重风险：≥80分
- 高风险：60-79分
- 中等风险：40-59分
- 低风险：<40分

## 👥 用户角色设定

| 角色 | 用户类型 | VIP等级 | 累计充值 | 预期风险等级 |
|-----|---------|---------|---------|-------------|
| **张三** | 高风险流失用户 | VIP8 | ¥25,000 | 🔴 严重风险 |
| **李四** | 中等风险用户 | VIP6 | ¥8,000 | 🔵 中等风险 |
| **王五** | 低风险稳定用户 | VIP7 | ¥15,000 | 🟢 低风险 |

---

## 📊 角色一：高风险流失用户

### 👤 用户基本信息

**用户：张三**
- **用户ID**: 10001
- **角色名**: 霸王张三
- **服务器**: 服务器1
- **VIP等级**: VIP8
- **注册时间**: 2024-01-15 (注册约7个月)
- **累计充值**: ¥25,000
- **首次充值**: 2024-01-20
- **最后充值**: 2025-07-15 (22天前)
- **最后登录**: 2025-08-01 (5天前)
- **总登录天数**: 180天
- **连续登录天数**: 2天

### 🔢 详细计算过程

#### 1️⃣ 登录风险计算 (权重: 25%)

**数据收集**：
- 最近30天登录次数：8次
- 距离上次登录：5天
- 连续登录天数：2天

**计算步骤**：

**登录频率风险** (权重: 40%)
```
期望登录次数 = 20次 (30天期间)
实际登录次数 = 8次
登录频率风险 = max(0, (20 - 8) / 20 × 100) = max(0, 60) = 60分
```

**登录间隔风险** (权重: 40%)
```
距上次登录天数 = 5天
登录间隔风险 = min(100, 5 × 5) = min(100, 25) = 25分
```

**连续登录风险** (权重: 20%)
```
期望连续登录 = 10天
实际连续登录 = 2天
连续登录风险 = max(0, (10 - 2) × 10) = max(0, 80) = 80分
```

**综合登录风险**：
```
登录风险 = 60 × 0.4 + 25 × 0.4 + 80 × 0.2
        = 24 + 10 + 16
        = 50分
```

#### 2️⃣ 充值风险计算 (权重: 30%)

**数据收集**：
- 历史充值记录：总计¥25,000，充值15次，时间跨度7个月
- 最近30天充值：0次，¥0
- 距离上次充值：22天

**计算步骤**：

**充值频率风险** (权重: 30%)
```
注册天数 = (2025-08-06) - (2024-01-15) = 204天
历史平均月充值次数 = 15次 / (204天 / 30天) = 15 / 6.8 = 2.2次/月
最近30天充值次数 = 0次
充值频率风险 = max(0, (2.2 - 0) / 2.2 × 100) = max(0, 100) = 100分
```

**充值间隔风险** (权重: 40%)
```
距上次充值天数 = 22天
充值间隔风险 = min(100, 22 × 3) = min(100, 66) = 66分
```

**充值金额风险** (权重: 30%)
```
历史月均充值金额 = ¥25,000 / 6.8个月 = ¥3,676/月
最近30天充值金额 = ¥0
充值金额风险 = max(0, (3676 - 0) / 3676 × 100) = max(0, 100) = 100分
```

**综合充值风险**：
```
充值风险 = 100 × 0.3 + 66 × 0.4 + 100 × 0.3
        = 30 + 26.4 + 30
        = 86.4分
```

#### 3️⃣ 活跃风险计算 (权重: 30%)

**数据收集**：
- 近7天平均在线时长：45分钟/天（历史30天平均：120分钟/天）
- 近7天平均任务完成度：25%（历史30天平均：70%）
- 连续未完成日常任务天数：4天

**计算步骤**：

**在线时长风险** (权重: 40%)
```
近7天平均在线时长 = 45分钟/天
历史30天平均在线时长 = 120分钟/天
在线时长风险 = max(0, (1 - 45/120) × 100) = max(0, 62.5) = 62.5分
```

**活跃度风险** (权重: 60%)
```
近7天平均任务完成度 = 25%
历史30天平均任务完成度 = 70%
活跃度风险 = max(0, (1 - 25/70) × 100) = max(0, 64.3) = 64.3分
```

**综合活跃风险**：
```
活跃风险 = 62.5 × 0.4 + 64.3 × 0.6
        = 25 + 38.6
        = 63.6分
```

### 📈 综合风险评分

```
综合风险评分 = 登录风险×0.30 + 充值风险×0.40 + 活跃风险×0.30
            = 50×0.30 + 86.4×0.40 + 63.6×0.30
            = 15 + 34.56 + 19.08
            = 68.64分
```

**风险等级**：🟠 **高风险** (60-79分区间)

**预测流失时间**：14天内

### 🏷️ 风险因素标签

张三的风险因素标签：
```json
[
    {
        "type": "recharge",
        "text": "充值风险", 
        "score": 86.4,
        "is_primary": true
    },
    {
        "type": "activity",
        "text": "活跃风险",
        "score": 63.6,
        "is_primary": false
    },
    {
        "type": "login",
        "text": "登录风险",
        "score": 50.0,
        "is_primary": false
    }
]
```

### 💡 建议干预措施

```json
[
    {
        "type": "contact",
        "text": "主动联系用户",
        "priority": "high",
        "reason": "充值中断22天且活跃度大幅下降，需要了解具体原因"
    },
    {
        "type": "discount",
        "text": "提供充值折扣",
        "priority": "high",
        "reason": "充值风险86.4分，可能对价格敏感"
    },
    {
        "type": "task_reward",
        "text": "发放任务奖励礼包",
        "priority": "high",
        "reason": "任务完成度从70%降到25%，需要激励参与"
    },
    {
        "type": "event",
        "text": "邀请参加限时活动",
        "priority": "medium",
        "reason": "在线时长减半，通过活动重新激发游戏兴趣"
    }
]
```

---

## 📊 角色二：中等风险用户

### 👤 用户基本信息

**用户：李四**
- **用户ID**: 10002
- **角色名**: 剑客李四
- **服务器**: 服务器2
- **VIP等级**: VIP6
- **注册时间**: 2024-03-10 (注册约5个月)
- **累计充值**: ¥8,000
- **首次充值**: 2024-03-15
- **最后充值**: 2025-07-28 (9天前)
- **最后登录**: 2025-08-05 (1天前)
- **总登录天数**: 120天
- **连续登录天数**: 8天

### 🔢 详细计算过程

#### 1️⃣ 登录风险计算 (权重: 25%)

**数据收集**：
- 最近30天登录次数：16次
- 距离上次登录：1天
- 连续登录天数：8天

**计算步骤**：

**登录频率风险** (权重: 40%)
```
期望登录次数 = 20次
实际登录次数 = 16次
登录频率风险 = max(0, (20 - 16) / 20 × 100) = max(0, 20) = 20分
```

**登录间隔风险** (权重: 40%)
```
距上次登录天数 = 1天
登录间隔风险 = min(100, 1 × 5) = min(100, 5) = 5分
```

**连续登录风险** (权重: 20%)
```
期望连续登录 = 10天
实际连续登录 = 8天
连续登录风险 = max(0, (10 - 8) × 10) = max(0, 20) = 20分
```

**综合登录风险**：
```
登录风险 = 20 × 0.4 + 5 × 0.4 + 20 × 0.2
        = 8 + 2 + 4
        = 14分
```

#### 2️⃣ 充值风险计算 (权重: 30%)

**数据收集**：
- 历史充值记录：总计¥8,000，充值12次，时间跨度5个月
- 最近30天充值：1次，¥500
- 距离上次充值：9天

**计算步骤**：

**充值频率风险** (权重: 30%)
```
注册天数 = (2025-08-06) - (2024-03-10) = 149天
历史平均月充值次数 = 12次 / (149天 / 30天) = 12 / 4.97 = 2.4次/月
最近30天充值次数 = 1次
充值频率风险 = max(0, (2.4 - 1) / 2.4 × 100) = max(0, 58.3) = 58.3分
```

**充值间隔风险** (权重: 40%)
```
距上次充值天数 = 9天
充值间隔风险 = min(100, 9 × 3) = min(100, 27) = 27分
```

**充值金额风险** (权重: 30%)
```
历史月均充值金额 = ¥8,000 / 4.97个月 = ¥1,610/月
最近30天充值金额 = ¥500
充值金额风险 = max(0, (1610 - 500) / 1610 × 100) = max(0, 68.9) = 68.9分
```

**综合充值风险**：
```
充值风险 = 58.3 × 0.3 + 27 × 0.4 + 68.9 × 0.3
        = 17.49 + 10.8 + 20.67
        = 48.96分
```

#### 3️⃣ 活跃风险计算 (权重: 30%)

**数据收集**：
- 近7天平均在线时长：85分钟/天（历史30天平均：100分钟/天）
- 近7天平均任务完成度：50%（历史30天平均：65%）
- 活跃度总体较稳定，仅轻微下降

**计算步骤**：

**在线时长风险** (权重: 40%)
```
近7天平均在线时长 = 85分钟/天
历史30天平均在线时长 = 100分钟/天
在线时长风险 = max(0, (1 - 85/100) × 100) = max(0, 15) = 15分
```

**活跃度风险** (权重: 60%)
```
近7天平均任务完成度 = 50%
历史30天平均任务完成度 = 65%
活跃度风险 = max(0, (1 - 50/65) × 100) = max(0, 23.1) = 23.1分
```

**综合活跃风险**：
```
活跃风险 = 15 × 0.4 + 23.1 × 0.6
        = 6 + 13.86
        = 19.86分
```

### 📈 综合风险评分

```
综合风险评分 = 14×0.30 + 48.96×0.40 + 19.86×0.30
            = 4.2 + 19.58 + 5.96
            = 29.74分
```

**风险等级**：🟢 **低风险** (<40分)

**预测流失时间**：无预测（风险较低）

### 💡 建议干预措施

```json
[
    {
        "type": "gift",
        "text": "赠送任务奖励礼包",
        "priority": "low",
        "reason": "任务完成度轻微下降，适度激励"
    },
    {
        "type": "event",
        "text": "推荐适合活动",
        "priority": "low",
        "reason": "保持游戏兴趣，防止活跃度进一步下降"
    },
    {
        "type": "monitor",
        "text": "持续监控充值行为",
        "priority": "medium",
        "reason": "充值金额下降较明显，需要关注发展趋势"
    }
]
```

---

## 📊 角色三：低风险稳定用户

### 👤 用户基本信息

**用户：王五**
- **用户ID**: 10003
- **角色名**: 法师王五
- **服务器**: 服务器1
- **VIP等级**: VIP7
- **注册时间**: 2024-02-01 (注册约6个月)
- **累计充值**: ¥15,000
- **首次充值**: 2024-02-05
- **最后充值**: 2025-08-03 (3天前)
- **最后登录**: 2025-08-06 (今天)
- **总登录天数**: 150天
- **连续登录天数**: 15天

### 🔢 详细计算过程

#### 1️⃣ 登录风险计算 (权重: 25%)

**数据收集**：
- 最近30天登录次数：25次
- 距离上次登录：0天
- 连续登录天数：15天

**计算步骤**：

**登录频率风险** (权重: 40%)
```
期望登录次数 = 20次
实际登录次数 = 25次
登录频率风险 = max(0, (20 - 25) / 20 × 100) = max(0, -25) = 0分
```

**登录间隔风险** (权重: 40%)
```
距上次登录天数 = 0天
登录间隔风险 = min(100, 0 × 5) = min(100, 0) = 0分
```

**连续登录风险** (权重: 20%)
```
期望连续登录 = 10天
实际连续登录 = 15天
连续登录风险 = max(0, (10 - 15) × 10) = max(0, -50) = 0分
```

**综合登录风险**：
```
登录风险 = 0 × 0.4 + 0 × 0.4 + 0 × 0.2 = 0分
```

#### 2️⃣ 充值风险计算 (权重: 30%)

**数据收集**：
- 历史充值记录：总计¥15,000，充值20次，时间跨度6个月
- 最近30天充值：3次，¥1,200
- 距离上次充值：3天

**计算步骤**：

**充值频率风险** (权重: 30%)
```
注册天数 = (2025-08-06) - (2024-02-01) = 187天
历史平均月充值次数 = 20次 / (187天 / 30天) = 20 / 6.23 = 3.2次/月
最近30天充值次数 = 3次
充值频率风险 = max(0, (3.2 - 3) / 3.2 × 100) = max(0, 6.25) = 6.25分
```

**充值间隔风险** (权重: 40%)
```
距上次充值天数 = 3天
充值间隔风险 = min(100, 3 × 3) = min(100, 9) = 9分
```

**充值金额风险** (权重: 30%)
```
历史月均充值金额 = ¥15,000 / 6.23个月 = ¥2,408/月
最近30天充值金额 = ¥1,200
充值金额风险 = max(0, (2408 - 1200) / 2408 × 100) = max(0, 50.17) = 50.17分
```

**综合充值风险**：
```
充值风险 = 6.25 × 0.3 + 9 × 0.4 + 50.17 × 0.3
        = 1.88 + 3.6 + 15.05
        = 20.53分
```

#### 3️⃣ 活跃风险计算 (权重: 30%)

**数据收集**：
- 近7天平均在线时长：135分钟/天（历史30天平均：125分钟/天）
- 近7天平均任务完成度：85%（历史30天平均：80%）
- 活跃度不仅稳定，甚至略有提升

**计算步骤**：

**在线时长风险** (权重: 40%)
```
近7天平均在线时长 = 135分钟/天
历史30天平均在线时长 = 125分钟/天
在线时长风险 = max(0, (1 - 135/125) × 100) = max(0, -8) = 0分
```

**活跃度风险** (权重: 60%)
```
近7天平均任务完成度 = 85%
历史30天平均任务完成度 = 80%
活跃度风险 = max(0, (1 - 85/80) × 100) = max(0, -6.25) = 0分
```

**综合活跃风险**：
```
活跃风险 = 0 × 0.4 + 0 × 0.6 = 0分
```

### 📈 综合风险评分

```
综合风险评分 = 0×0.30 + 20.53×0.40 + 0×0.30
            = 0 + 8.21 + 0
            = 8.21分
```

**风险等级**：🟢 **低风险** (<40分)

**预测流失时间**：无预测（用户稳定）

### 💡 建议干预措施

```json
[
    {
        "type": "maintenance",
        "text": "正常维护关系",
        "priority": "low",
        "reason": "用户状态良好，保持现状"
    },
    {
        "type": "reward",
        "text": "忠实用户奖励",
        "priority": "low",
        "reason": "奖励忠实用户，增强归属感"
    }
]
```

---

## 📊 计算结果对比

### 综合评分对比表

| 用户 | 登录风险 | 充值风险 | 活跃风险 | **综合评分** | **风险等级** | **预测流失时间** |
|------|---------|---------|---------|-------------|-------------|-----------------|
| **张三** | 50分 | 86.4分 | 63.6分 | **68.64分** | 🟠 高风险 | 14天内 |
| **李四** | 14分 | 48.96分 | 19.86分 | **29.74分** | 🟢 低风险 | 无预测 |
| **王五** | 0分 | 20.53分 | 0分 | **8.21分** | 🟢 低风险 | 无预测 |

### 权重贡献分析

| 用户 | 登录贡献 | 充值贡献 | 活跃贡献 | 总计 |
|------|---------|---------|---------|------|
| **张三** | 15分 | **34.56分** | 19.08分 | 68.64分 |
| **李四** | 4.2分 | **19.58分** | 5.96分 | 29.74分 |
| **王五** | 0分 | **8.21分** | 0分 | 8.21分 |

**分析结论**：充值风险是影响用户流失预测的最关键因素，权重设置合理。

## 🎯 风险因素分析

### 主要风险因素识别

#### 张三（高风险用户）的主要问题：
1. **充值中断**：22天未充值，充值风险86.4分（最高）
2. **活跃度大幅下降**：在线时长从120分钟降到45分钟，任务完成度从70%降到25%
3. **登录频率下降**：30天仅登录8次，远低于期望的20次
4. **综合表现**：三个维度均出现明显问题，预示即将流失

#### 李四（低风险用户）的潜在风险点：
1. **充值金额下降**：月充值从¥1,610降到¥500，需要持续观察
2. **活跃度轻微下降**：在线时长和任务完成度均有小幅下降
3. **整体风险可控**：各项指标下降幅度有限，属于正常波动范围

#### 王五（低风险用户）的优势：
1. **登录高活跃**：超出期望值，连续登录15天
2. **充值稳定**：频率和时间间隔都正常，金额略有下降但仍在合理范围
3. **活跃度提升**：在线时长和任务完成度均有提升，表现出良好的参与度

## 💡 干预措施建议

### 分级干预策略

#### 🔴 严重/高风险用户 (≥60分)
**张三类型用户的干预措施**：
1. **立即联系**：客服主动电话/消息联系
2. **问题调研**：了解流失原因（游戏体验、经济因素、时间精力等）
3. **个性化挽留**：
   - 充值折扣：提供限时充值优惠（针对充值风险86.4分）
   - 任务奖励：发放高价值任务奖励包（针对活跃度下降）
   - VIP特权：赠送免登录奖励、经验加成等（降低参与门槛）
   - 专属客服：安排专人跟进服务
4. **活跃度激励**：推荐轻松完成的日常任务，降低参与难度
5. **效果跟踪**：7天内必须完成首次联系，14天内评估效果

#### 🔵 中等风险用户 (40-59分)  
*注：当前李四评分29.74分，已降至低风险，此策略适用于40-59分区间用户*
**中等风险用户的干预措施**：
1. **关怀提醒**：游戏内消息或邮件关怀
2. **适度激励**：
   - 任务奖励：针对活跃度下降发放任务激励礼包
   - 充值优惠：适度的充值折扣活动
   - 活动推荐：推送适合的游戏活动
3. **定期跟进**：每月评估一次状态变化
4. **预防为主**：防止风险等级继续上升，重点关注充值和活跃度趋势

#### 🟢 低风险用户 (<40分)
**王五类型用户的维护策略**：
1. **正常维护**：保持现有服务质量
2. **忠诚奖励**：定期的忠实用户奖励
3. **口碑培养**：邀请推荐新用户
4. **长期关系**：建立长期稳定的用户关系

### 干预时机选择

| 风险等级 | 干预时机 | 联系频率 | 措施强度 |
|---------|---------|---------|---------|
| 🔴 严重风险 | 24小时内 | 每周 | 高强度 |
| 🟠 高风险 | 48小时内 | 每2周 | 中高强度 |
| 🔵 中等风险 | 7天内 | 每月 | 中等强度 |
| 🟢 低风险 | 按需 | 按需 | 低强度 |

## 📈 效果评估指标

### 干预成功率指标

**预期干预效果**：
- **严重风险用户**：50%成功挽回率
- **高风险用户**：70%风险等级下降
- **中等风险用户**：85%保持稳定或改善
- **低风险用户**：95%保持低风险状态

### 关键监控指标

1. **风险等级变化**：干预后30天内的风险等级变化
2. **行为指标恢复**：登录频率、充值行为、活跃度恢复程度
3. **流失率对比**：干预用户 vs 未干预用户的实际流失率
4. **ROI计算**：干预成本 vs 用户价值保留收益

---

## 🔧 实际应用建议

### 系统配置优化

基于案例分析，建议的系统配置调优：

```python
# 新的三维度权重配置
RISK_FACTOR_WEIGHTS = {
    'login_frequency': 0.30,      # 登录风险权重30%
    'recharge_frequency': 0.40,   # 充值风险权重40% (最关键)
    'activity_risk': 0.30,        # 活跃风险权重30% (在线时长+任务完成度)
}

# 活跃风险子权重配置
ACTIVITY_RISK_WEIGHTS = {
    'online_time_risk': 0.40,     # 在线时长风险权重40%
    'task_completion_risk': 0.60, # 任务完成度风险权重60%
}

# 风险阈值保持不变
RISK_THRESHOLDS = {
    'critical': 80.0,    # 严重风险阈值
    'high': 60.0,        # 高风险阈值  
    'medium': 40.0,      # 中等风险阈值
}
```

### 监控告警设置

```python
# 自动告警条件
ALERT_CONDITIONS = {
    'critical_user_increase': '严重风险用户数增加>20%',
    'high_value_user_risk': 'VIP8+用户进入高风险状态',
    'activity_risk_spike': '活跃风险突然升高的用户>10人/天',
    'consecutive_login_drop': '连续3天未登录的高价值用户>5人',
    'task_completion_plunge': '任务完成度骤降>50%的用户>20人/天',
    'intervention_failure': '干预失败率>30%'
}
```

---

*文档版本: v1.0*  
*最后更新: 2025-08-06*  
*案例设计: VIPManager 开发团队*

---

**📝 使用说明**：
1. 本文档中的用户数据为虚拟案例，仅用于演示计算过程
2. 实际应用中需要根据真实用户数据进行计算
3. 风险因子权重可根据实际效果进行调优
4. 建议定期更新案例数据以反映最新的用户行为模式