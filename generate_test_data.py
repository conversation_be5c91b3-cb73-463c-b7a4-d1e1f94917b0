#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成测试数据脚本
运行方式: python manage.py shell < generate_test_data.py
"""

import os
import django
import random
from datetime import datetime, timedelta, date
from decimal import Decimal
from django.utils import timezone

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'VIPManager.settings')
django.setup()

from big_r_overview.models import (
    BigRUser, UserRechargeRecord, UserLoginRecord, 
    BigROverviewStats, UserBehaviorAnalysis
)
from churn_warning.models import (
    ChurnRiskFactor, ChurnPrediction, ChurnWarningStats, 
    ChurnInterventionLog, ChurnWarningConfig
)
from privileges.models import (
    VIPLevel, PrivilegeCategory, Privilege, 
    PrivilegeUsageLog, PrivilegeStats
)
from service_analysis.models import PrivilegeProcessRecord

print("开始生成测试数据...")

# 1. 生成VIP等级数据
print("1. 生成VIP等级数据...")
vip_levels = [
    {"level": 0, "name": "普通用户", "threshold": Decimal("0"), "description": "未充值用户"},
    {"level": 1, "name": "青铜会员", "threshold": Decimal("100"), "description": "首次充值用户"},
    {"level": 2, "name": "白银会员", "threshold": Decimal("500"), "description": "小额充值用户"},
    {"level": 3, "name": "黄金会员", "threshold": Decimal("1000"), "description": "中等充值用户"},
    {"level": 4, "name": "铂金会员", "threshold": Decimal("2000"), "description": "高额充值用户"},
    {"level": 5, "name": "钻石会员", "threshold": Decimal("5000"), "description": "VIP用户"},
    {"level": 6, "name": "大师会员", "threshold": Decimal("10000"), "description": "大R用户"},
    {"level": 7, "name": "宗师会员", "threshold": Decimal("20000"), "description": "超级大R"},
    {"level": 8, "name": "王者会员", "threshold": Decimal("50000"), "description": "顶级大R"},
    {"level": 9, "name": "传奇会员", "threshold": Decimal("100000"), "description": "神豪用户"},
    {"level": 10, "name": "至尊会员", "threshold": Decimal("200000"), "description": "终极神豪"},
]

for vip_data in vip_levels:
    VIPLevel.objects.get_or_create(level=vip_data['level'], defaults=vip_data)

# 2. 生成特权分类数据
print("2. 生成特权分类数据...")
categories = [
    {
        "name": "item_reward",
        "display_name": "道具奖励",
        "icon": "gift",
        "color": "#f59e0b",
        "description": "游戏内道具和物品奖励",
        "sort_order": 1
    },
    {
        "name": "coin_reward", 
        "display_name": "金币奖励",
        "icon": "coin",
        "color": "#eab308",
        "description": "金币和货币奖励",
        "sort_order": 2
    },
    {
        "name": "attribute_bonus",
        "display_name": "属性加成",
        "icon": "trend-up",
        "color": "#22c55e", 
        "description": "角色属性加成和增益",
        "sort_order": 3
    },
    {
        "name": "identity_mark",
        "display_name": "身份标识",
        "icon": "crown",
        "color": "#a855f7",
        "description": "VIP身份标识和特殊称号",
        "sort_order": 4
    },
    {
        "name": "security_protection",
        "display_name": "安全保障", 
        "icon": "shield",
        "color": "#3b82f6",
        "description": "账号安全保障服务",
        "sort_order": 5
    }
]

for cat_data in categories:
    PrivilegeCategory.objects.get_or_create(name=cat_data['name'], defaults=cat_data)

# 3. 生成特权数据
print("3. 生成特权数据...")
categories_map = {cat.name: cat for cat in PrivilegeCategory.objects.all()}
vip_levels_map = {vip.level: vip for vip in VIPLevel.objects.all()}

privileges_data = [
    # 道具奖励类
    {
        "name": "每日登录礼包",
        "category": categories_map["item_reward"],
        "description": "每日登录可获得专属礼包奖励",
        "min_vip_level": vip_levels_map[1],
        "sort_order": 1,
        "is_featured": True
    },
    {
        "name": "每周专属宝箱",
        "category": categories_map["item_reward"], 
        "description": "每周可领取专属VIP宝箱",
        "min_vip_level": vip_levels_map[3],
        "sort_order": 2
    },
    {
        "name": "限时活动双倍奖励",
        "category": categories_map["item_reward"],
        "description": "参与限时活动获得双倍奖励",
        "min_vip_level": vip_levels_map[5],
        "sort_order": 3,
        "is_featured": True
    },
    
    # 金币奖励类
    {
        "name": "每日金币奖励",
        "category": categories_map["coin_reward"],
        "description": "每日可领取固定金币奖励",
        "min_vip_level": vip_levels_map[1],
        "sort_order": 4
    },
    {
        "name": "充值返利奖励", 
        "category": categories_map["coin_reward"],
        "description": "充值时获得额外金币返利",
        "min_vip_level": vip_levels_map[2],
        "sort_order": 5,
        "is_featured": True
    },
    {
        "name": "PVP胜利奖励加成",
        "category": categories_map["coin_reward"],
        "description": "PVP获胜时金币奖励翻倍",
        "min_vip_level": vip_levels_map[4],
        "sort_order": 6
    },
    
    # 属性加成类
    {
        "name": "经验值获取加成",
        "category": categories_map["attribute_bonus"],
        "description": "获得经验值时享受加成效果",
        "min_vip_level": vip_levels_map[2],
        "sort_order": 7,
        "is_featured": True
    },
    {
        "name": "攻击力永久提升",
        "category": categories_map["attribute_bonus"],
        "description": "角色攻击力获得永久提升",
        "min_vip_level": vip_levels_map[6],
        "sort_order": 8
    },
    {
        "name": "生命值上限增加",
        "category": categories_map["attribute_bonus"],
        "description": "角色生命值上限永久增加",
        "min_vip_level": vip_levels_map[7],
        "sort_order": 9
    },
    
    # 身份标识类
    {
        "name": "VIP专属称号",
        "category": categories_map["identity_mark"],
        "description": "获得专属VIP称号显示",
        "min_vip_level": vip_levels_map[1],
        "sort_order": 10,
        "is_featured": True
    },
    {
        "name": "聊天气泡特效",
        "category": categories_map["identity_mark"],
        "description": "聊天时显示特殊气泡特效",
        "min_vip_level": vip_levels_map[3],
        "sort_order": 11
    },
    {
        "name": "专属坐骑皮肤",
        "category": categories_map["identity_mark"],
        "description": "解锁VIP专属坐骑皮肤",
        "min_vip_level": vip_levels_map[8],
        "sort_order": 12
    },
    
    # 安全保障类
    {
        "name": "24小时客服优先",
        "category": categories_map["security_protection"],
        "description": "享受24小时客服优先处理服务",
        "min_vip_level": vip_levels_map[3],
        "sort_order": 13
    },
    {
        "name": "账号安全保护",
        "category": categories_map["security_protection"],
        "description": "提供额外的账号安全保护措施",
        "min_vip_level": vip_levels_map[5],
        "sort_order": 14,
        "is_featured": True
    },
    {
        "name": "误删数据恢复",
        "category": categories_map["security_protection"],
        "description": "意外删除的游戏数据可申请恢复",
        "min_vip_level": vip_levels_map[9],
        "sort_order": 15
    }
]

for priv_data in privileges_data:
    Privilege.objects.create(**priv_data)

# 4. 生成大R用户数据
print("4. 生成大R用户数据...")
user_names = [
    "龙傲天", "叶凡", "萧炎", "林动", "牧尘", "秦羽", "韩立", "方运", "王林", "孟浩",
    "萧逸风", "李七夜", "姬昊", "石昊", "叶天帝", "无始大帝", "狠人女帝", "青帝", "帝尊", "天帝",
    "独孤求败", "令狐冲", "张无忌", "郭靖", "杨过", "韦小宝", "段誉", "虚竹", "乔峰", "萧峰",
    "剑圣", "剑神", "剑仙", "剑帝", "刀皇", "枪神", "拳王", "医仙", "毒王", "丹神",
    "霸天虎", "威震天", "擎天柱", "大黄蜂", "铁皮", "爵士", "救护车", "红蜘蛛", "震荡波", "声波"
]

server_names = ["天启", "纵横", "霸业", "传奇", "无双", "至尊", "王者", "帝尊"]

big_r_users = []
for i in range(100):
    # 随机充值金额分布
    recharge_weights = [0.4, 0.3, 0.15, 0.1, 0.05]  # 低到高的权重
    recharge_ranges = [
        (100, 1000),      # 40% - 小R
        (1000, 5000),     # 30% - 中R  
        (5000, 20000),    # 15% - 大R
        (20000, 100000),  # 10% - 超级大R
        (100000, 500000)  # 5%  - 神豪
    ]
    
    range_idx = random.choices(range(len(recharge_ranges)), weights=recharge_weights)[0]
    min_recharge, max_recharge = recharge_ranges[range_idx]
    total_recharge = Decimal(str(random.randint(min_recharge, max_recharge)))
    
    # 生成时间
    created_time = timezone.now() - timedelta(days=random.randint(30, 365))
    first_recharge = created_time + timedelta(days=random.randint(1, 7))
    last_recharge = first_recharge + timedelta(days=random.randint(1, 90))
    last_login = timezone.now() - timedelta(days=random.randint(0, 30))
    
    # 活跃度
    total_login_days = random.randint(20, 300)
    consecutive_login_days = random.randint(0, 30)
    
    # 风险标识  
    is_churn_risk = random.choice([True, False]) if random.random() < 0.3 else False
    churn_levels = ['low', 'medium', 'high', 'critical']
    churn_risk_level = random.choice(churn_levels) if is_churn_risk else 'low'
    
    user = BigRUser.objects.create(
        user_id=10000 + i,
        username=f"player_{10000+i}",
        character_name=random.choice(user_names) + str(random.randint(1, 999)),
        server_id=random.randint(1, 8),
        server_name=random.choice(server_names) + "服",
        total_recharge=total_recharge,
        first_recharge_date=first_recharge,
        last_recharge_date=last_recharge,
        last_login_date=last_login,
        total_login_days=total_login_days,
        consecutive_login_days=consecutive_login_days,
        is_potential=random.choice([True, False]) if random.random() < 0.25 else False,
        is_churn_risk=is_churn_risk,
        churn_risk_level=churn_risk_level,
        created_at=created_time
    )
    big_r_users.append(user)

# 5. 生成充值记录
print("5. 生成充值记录...")
payment_methods = ["微信支付", "支付宝", "银行卡", "Apple Pay", "Google Pay"]
for user in big_r_users[:50]:  # 为前50个用户生成充值记录
    recharge_count = random.randint(1, 10)
    total_amount = user.total_recharge
    
    for j in range(recharge_count):
        if j == recharge_count - 1:  # 最后一次充值使用剩余金额
            amount = total_amount
        else:
            max_amount = min(total_amount * Decimal("0.4"), total_amount - Decimal("100"))
            if max_amount > 100:
                amount = Decimal(str(random.randint(100, int(max_amount))))
            else:
                amount = Decimal("100")
            total_amount -= amount
            
        recharge_date = user.first_recharge_date + timedelta(
            days=random.randint(0, (user.last_recharge_date - user.first_recharge_date).days)
        )
        
        UserRechargeRecord.objects.create(
            user=user,
            order_id=f"PAY{user.user_id}{j:03d}{random.randint(1000, 9999)}",
            amount=amount,
            currency="CNY",
            payment_method=random.choice(payment_methods),
            status="success",
            paid_at=recharge_date
        )

# 6. 生成登录记录  
print("6. 生成登录记录...")
for user in big_r_users[:30]:  # 为前30个用户生成登录记录
    # 生成最近30天的登录记录
    for i in range(user.total_login_days):
        login_date = (timezone.now() - timedelta(days=30)).date() + timedelta(days=i)
        if login_date > timezone.now().date():
            break
            
        if random.random() < 0.7:  # 70%概率登录
            UserLoginRecord.objects.create(
                user=user,
                login_date=login_date,
                login_time=timezone.make_aware(
                    datetime.combine(login_date, datetime.min.time()) + 
                    timedelta(hours=random.randint(8, 23), minutes=random.randint(0, 59))
                ),
                online_duration=random.randint(30, 480),  # 30分钟到8小时
                ip_address=f"192.168.{random.randint(1,255)}.{random.randint(1,255)}",
                device_type=random.choice(["iOS", "Android", "PC"])
            )

# 7. 生成用户行为分析
print("7. 生成用户行为分析...")
for user in big_r_users:
    UserBehaviorAnalysis.objects.create(
        user=user,
        avg_recharge_amount=user.total_recharge / max(1, UserRechargeRecord.objects.filter(user=user).count()),
        recharge_frequency=random.uniform(0.5, 8.0),  # 每月充值频率
        days_since_last_recharge=(timezone.now().date() - user.last_recharge_date.date()).days if user.last_recharge_date else 0,
        avg_online_duration=random.randint(60, 300),  # 平均每日在线时长
        login_frequency=random.uniform(3.0, 7.0),  # 每周登录天数
        days_since_last_login=(timezone.now().date() - user.last_login_date.date()).days if user.last_login_date else 0,
        potential_score=random.uniform(20.0, 95.0),
        churn_risk_score=random.uniform(10.0, 90.0)
    )

# 8. 生成流失风险因子配置
print("8. 生成流失风险因子配置...")
risk_factors = [
    {
        "factor_type": "login_frequency",
        "description": "用户登录频率低于正常水平",
        "weight": 0.25,
        "threshold_value": 3.0
    },
    {
        "factor_type": "login_duration", 
        "description": "用户单次登录时长过短",
        "weight": 0.15,
        "threshold_value": 30.0
    },
    {
        "factor_type": "recharge_frequency",
        "description": "用户充值频率明显下降", 
        "weight": 0.30,
        "threshold_value": 1.0
    },
    {
        "factor_type": "recharge_amount",
        "description": "用户充值金额大幅减少",
        "weight": 0.20,
        "threshold_value": 500.0
    },
    {
        "factor_type": "activity_risk",
        "description": "用户活跃度显著降低",
        "weight": 0.10,
        "threshold_value": 50.0
    }
]

for factor_data in risk_factors:
    ChurnRiskFactor.objects.create(**factor_data)

# 9. 生成流失预警配置
print("9. 生成流失预警配置...")
ChurnWarningConfig.objects.create(
    critical_threshold=80.0,
    high_threshold=60.0,
    medium_threshold=40.0,
    prediction_days=30,
    min_confidence_score=0.7,
    auto_contact_critical=True,
    auto_gift_threshold=85.0,
    stats_retention_days=90
)

# 10. 生成流失预测数据
print("10. 生成流失预测数据...")
risk_users = [user for user in big_r_users if user.is_churn_risk]
for user in risk_users:
    behavior = UserBehaviorAnalysis.objects.get(user=user)
    
    # 根据用户风险等级设置评分
    risk_score_ranges = {
        'low': (20, 39),
        'medium': (40, 59), 
        'high': (60, 79),
        'critical': (80, 95)
    }
    min_score, max_score = risk_score_ranges[user.churn_risk_level]
    risk_score = random.uniform(min_score, max_score)
    
    # 生成风险因素
    risk_factors = []
    if behavior.days_since_last_login > 7:
        risk_factors.append({
            "factor": "长时间未登录",
            "value": behavior.days_since_last_login,
            "is_primary": True
        })
    if behavior.days_since_last_recharge > 30:
        risk_factors.append({
            "factor": "长时间未充值", 
            "value": behavior.days_since_last_recharge,
            "is_primary": True
        })
    if behavior.login_frequency < 3:
        risk_factors.append({
            "factor": "登录频率过低",
            "value": behavior.login_frequency,
            "is_primary": False
        })
    
    # 生成建议措施
    suggested_actions = []
    if risk_score >= 80:
        suggested_actions.extend([
            "立即联系用户了解情况",
            "赠送高价值礼包",
            "邀请参与限时活动"
        ])
    elif risk_score >= 60:
        suggested_actions.extend([
            "发送关怀短信",
            "推送个性化优惠",
            "客服主动联系"
        ])
    else:
        suggested_actions.extend([
            "推送游戏更新通知",
            "邀请参与日常活动"
        ])
    
    ChurnPrediction.objects.create(
        user=user,
        risk_level=user.churn_risk_level,
        risk_score=risk_score,
        login_risk_score=random.uniform(0, 100),
        recharge_risk_score=random.uniform(0, 100),
        activity_risk_score=random.uniform(0, 100),
        predicted_churn_date=timezone.now().date() + timedelta(days=random.randint(7, 60)),
        confidence_score=random.uniform(0.6, 0.95),
        risk_factors=risk_factors,
        suggested_actions=suggested_actions,
        status=random.choice(['pending', 'contacted', 'intervened'])
    )

# 11. 生成特权处理记录
print("11. 生成特权处理记录...")
processors = ["客服小王", "客服小李", "客服小张", "管理员", "系统"]
privileges = list(Privilege.objects.all())

for i in range(200):  # 生成200条处理记录
    user = random.choice(big_r_users)
    privilege = random.choice(privileges)
    
    # 确保用户VIP等级满足特权要求
    if user.vip_level >= privilege.min_vip_level.level:
        process_details = [
            f"为用户{user.character_name}处理{privilege.name}申请",
            f"用户{user.character_name}使用{privilege.name}特权",
            f"系统自动为VIP{user.vip_level}用户{user.character_name}发放{privilege.name}",
            f"客服为用户{user.character_name}手动发放{privilege.name}奖励"
        ]
        
        PrivilegeProcessRecord.objects.create(
            character_name=user.character_name,
            character_id=user.user_id,
            vip_level=user.vip_level,
            privilege=privilege,
            process_detail=random.choice(process_details),
            processor=random.choice(processors)
        )

# 12. 生成特权使用记录
print("12. 生成特权使用记录...")
for i in range(150):  # 生成150条使用记录
    user = random.choice(big_r_users)
    privilege = random.choice(privileges)
    
    if user.vip_level >= privilege.min_vip_level.level:
        usage_date = timezone.now().date() - timedelta(days=random.randint(0, 30))
        
        PrivilegeUsageLog.objects.get_or_create(
            privilege=privilege,
            user_id=user.user_id,
            usage_date=usage_date,
            defaults={'usage_count': random.randint(1, 5)}
        )

# 13. 生成特权统计数据
print("13. 生成特权统计数据...")
for privilege in privileges:
    # 计算统计数据
    usage_logs = PrivilegeUsageLog.objects.filter(privilege=privilege)
    process_records = PrivilegeProcessRecord.objects.filter(privilege=privilege)
    
    total_users = usage_logs.values('user_id').distinct().count()
    total_usage = sum(log.usage_count for log in usage_logs)
    
    PrivilegeStats.objects.create(
        privilege=privilege,
        total_users=total_users,
        active_users_today=random.randint(0, total_users//3),
        active_users_week=random.randint(total_users//3, total_users//2),
        active_users_month=total_users,
        total_usage=total_usage,
        avg_usage_per_user=total_usage / max(1, total_users),
        popularity_score=random.uniform(1.0, 10.0)
    )

# 14. 生成大R总览统计数据
print("14. 生成大R总览统计数据...")
for i in range(30):  # 生成最近30天的统计数据
    stat_date = timezone.now().date() - timedelta(days=i)
    
    # 模拟数据变化
    base_users = len(big_r_users)
    daily_variation = random.randint(-2, 5)  # 每日变化
    
    BigROverviewStats.objects.create(
        stat_date=stat_date,
        total_big_r_users=base_users + daily_variation,
        total_potential_users=len([u for u in big_r_users if u.is_potential]),
        total_churn_warning_users=len([u for u in big_r_users if u.is_churn_risk]),
        total_revenue=sum(u.total_recharge for u in big_r_users),
        avg_arpu=sum(u.total_recharge for u in big_r_users) / len(big_r_users),
        vip_distribution={
            f"vip_{level}": len([u for u in big_r_users if u.vip_level == level])
            for level in range(0, 11)
        },
        new_big_r_users=random.randint(0, 3),
        lost_big_r_users=random.randint(0, 1)
    )

# 15. 生成流失预警统计数据
print("15. 生成流失预警统计数据...")
for i in range(30):  # 生成最近30天的统计数据
    stat_date = timezone.now().date() - timedelta(days=i)
    
    # 按风险等级统计用户数量
    critical_count = len([u for u in big_r_users if u.churn_risk_level == 'critical'])
    high_count = len([u for u in big_r_users if u.churn_risk_level == 'high'])
    medium_count = len([u for u in big_r_users if u.churn_risk_level == 'medium'])
    low_count = len([u for u in big_r_users if u.churn_risk_level == 'low'])
    
    ChurnWarningStats.objects.create(
        stat_date=stat_date,
        critical_users=critical_count + random.randint(-1, 2),
        high_risk_users=high_count + random.randint(-2, 3),
        medium_risk_users=medium_count + random.randint(-3, 4),
        low_risk_users=low_count + random.randint(-5, 5),
        contacted_users=random.randint(0, critical_count + high_count),
        intervened_users=random.randint(0, critical_count),
        recovered_users=random.randint(0, critical_count//2),
        churned_users=random.randint(0, 2),
        intervention_success_rate=random.uniform(30.0, 80.0),
        avg_risk_score=random.uniform(30.0, 70.0),
        critical_change=random.randint(-2, 2),
        high_risk_change=random.randint(-3, 3),
        medium_risk_change=random.randint(-4, 4),
        low_risk_change=random.randint(-5, 5)
    )

print("测试数据生成完成！")
print(f"生成了 {len(big_r_users)} 个大R用户")
print(f"生成了 {UserRechargeRecord.objects.count()} 条充值记录")
print(f"生成了 {UserLoginRecord.objects.count()} 条登录记录")
print(f"生成了 {ChurnPrediction.objects.count()} 条流失预测记录")
print(f"生成了 {PrivilegeProcessRecord.objects.count()} 条特权处理记录")
print(f"生成了 {PrivilegeUsageLog.objects.count()} 条特权使用记录")
print(f"生成了 {Privilege.objects.count()} 个特权项目")
print(f"生成了 {VIPLevel.objects.count()} 个VIP等级")