# 流失预警中心 - 风险评判机制详细说明

## 📋 目录

1. [概述](#概述)
2. [风险评判体系](#风险评判体系)
3. [风险计算规则](#风险计算规则)
4. [数据库表关联关系](#数据库表关联关系)
5. [算法详细说明](#算法详细说明)
6. [配置参数](#配置参数)
7. [使用示例](#使用示例)

## 🎯 概述

流失预警中心采用多维度综合评分机制，通过分析用户的登录行为、充值行为、活跃风险三个核心维度，计算出用户的流失风险评分，并自动分类到不同的风险等级。

### 核心特点

- **多维度评估**: 登录、充值、活跃三个维度综合评判
- **权重可配置**: 每个风险因子的权重可以动态调整
- **实时计算**: 基于最新的用户行为数据实时更新风险评分
- **智能预测**: 根据风险评分预测用户流失时间
- **自动分级**: 自动将用户分类到低、中、高、严重四个风险等级

## 🏗️ 风险评判体系

### 风险等级定义

| 风险等级 | 评分区间 | 颜色标识 | 描述 | 预计流失时间 |
|---------|---------|---------|------|-------------|
| **严重风险** (Critical) | 80-100分 | 🔴 红色 | 极高流失可能，需要立即干预 | 7天内 |
| **高风险** (High) | 60-79分 | 🟠 橙色 | 高流失可能，需要主动联系 | 14天内 |
| **中等风险** (Medium) | 40-59分 | 🔵 蓝色 | 中等流失可能，需要关注 | 30天内 |
| **低风险** (Low) | 0-39分 | 🟢 绿色 | 流失可能性较低，正常维护 | 无预测 |

### 四维度评估模型

```
总风险评分 = (登录风险 × 权重₁) + (充值风险 × 权重₂) + (活跃风险 × 权重₃)
```

**默认权重配置：**
- 登录风险权重：0.30 (30%)
- 充值风险权重：0.40 (40%) - 最高权重，因为充值行为最能反映用户价值和流失意图
- 活跃风险权重：0.30 (30%) - 包含在线时长和活跃度两个关键指标

## ⚙️ 风险计算规则

### 1. 登录风险计算 (Login Risk)

**数据来源**: `big_r_overview_bigruser` + `big_r_overview_userloginrecord`

**计算维度**:

#### 1.1 登录频率风险 (权重: 40%)
```python
# 期望值：30天内登录20次
recent_logins = 最近30天登录天数
expected_logins = 20
login_frequency_score = max(0, (expected_logins - recent_logins) / expected_logins × 100)
```

#### 1.2 登录间隔风险 (权重: 40%)
```python
# 距离上次登录天数，每天增加5分风险
days_since_login = 当前时间 - 最后登录时间 (天数)
login_recency_score = min(100, days_since_login × 5)
```

#### 1.3 连续登录风险 (权重: 20%)
```python
# 连续登录天数越少风险越高，期望连续登录10天
expected_consecutive = 10
consecutive_score = max(0, (expected_consecutive - 连续登录天数) × 10)
```

**综合登录风险**:
```python
login_risk = login_frequency_score × 0.4 + login_recency_score × 0.4 + consecutive_score × 0.2
```

### 2. 充值风险计算 (Recharge Risk)

**数据来源**: `big_r_overview_bigruser` + `big_r_overview_userrechargerecord`

**计算维度**:

#### 2.1 充值频率风险 (权重: 30%)
```python
# 对比历史平均充值频率
历史平均月充值次数 = 总充值次数 / 注册月数
最近30天充值次数 = recent_count
frequency_risk = max(0, (历史平均月充值次数 - 最近30天充值次数) / 历史平均月充值次数 × 100)
```

#### 2.2 充值间隔风险 (权重: 40%)
```python
# 距离上次充值天数，每天增加3分风险
days_since_recharge = 当前时间 - 最后充值时间 (天数)
recency_risk = min(100, days_since_recharge × 3)
```

#### 2.3 充值金额风险 (权重: 30%)
```python
# 对比历史平均充值金额
历史月均充值金额 = 累计充值金额 / 注册月数
最近30天充值金额 = recent_amount
amount_risk = max(0, (历史月均充值金额 - 最近30天充值金额) / 历史月均充值金额 × 100)
```

**综合充值风险**:
```python
recharge_risk = frequency_risk × 0.3 + recency_risk × 0.4 + amount_risk × 0.3
```

### 3. 活跃风险计算 (Activity Risk)

**数据来源**: 用户行为记录表 (在线时长、日常任务完成记录)

**计算维度**:

#### 3.1 在线时长风险 (权重: 40%)
```python
# 计算用户近7天平均在线时长与历史30天平均在线时长的比值
近7天平均在线时长 = sum(最近7天每日在线时长) / 7
历史30天平均在线时长 = sum(历史30天每日在线时长) / 30
在线时长风险 = max(0, (1 - 近7天平均 / 历史30天平均) × 100)
```

#### 3.2 活跃度风险 (权重: 60%)
```python
# 基于日常任务完成度计算
近7天平均完成度 = sum(最近7天任务完成度) / 7  # 任务完成度0-100%
历史30天平均完成度 = sum(历史30天任务完成度) / 30
活跃度风险 = max(0, (1 - 近7天完成度 / 历史30天完成度) × 100)
```

#### 3.3 特殊情况处理
```python
# 连续3天未登录直接标记为高风险
if 连续未登录天数 >= 3:
    在线时长风险 = 100
    活跃度风险 = 100

# 新用户（注册少于30天）使用行业平均值作为基准
if 注册天数 < 30:
    行业平均在线时长 = 120分钟/天
    行业平均任务完成度 = 60%
    # 使用行业平均值替代历史平均值进行计算
```

**综合活跃风险**:
```python
activity_risk = 在线时长风险 × 0.4 + 活跃度风险 × 0.6
```

## 🗄️ 数据库表关联关系

### 核心表结构

```
churn_warning_churnprediction (流失预测主表)
├── user_id (外键) → big_r_overview_bigruser.id
├── risk_score (综合风险评分)
├── login_risk_score (登录风险评分)
├── recharge_risk_score (充值风险评分)
├── activity_risk_score (活跃风险评分)
└── risk_level (风险等级: low/medium/high/critical)
```

### 表关联图

```mermaid
graph TD
    A[big_r_overview_bigruser<br/>大R用户基础表] --> B[churn_warning_churnprediction<br/>流失预测表]
    C[big_r_overview_userloginrecord<br/>登录记录表] --> B
    D[big_r_overview_userrechargerecord<br/>充值记录表] --> B
    E[churn_warning_churnriskfactor<br/>风险因子配置表] --> B
    B --> F[churn_warning_churninterventionlog<br/>干预记录表]
    B --> G[churn_warning_churnwarningstats<br/>统计数据表]
    H[churn_warning_churnwarningconfig<br/>系统配置表] --> B
```

### 详细表关联说明

#### 1. 主表关联
- **churn_warning_churnprediction.user_id** ← **big_r_overview_bigruser.id** (一对一)
  - 每个大R用户对应一条流失预测记录

#### 2. 数据源关联
- **big_r_overview_userloginrecord.user_id** ← **big_r_overview_bigruser.id** (一对多)
  - 用于计算登录风险评分
  
- **big_r_overview_userrechargerecord.user_id** ← **big_r_overview_bigruser.id** (一对多)
  - 用于计算充值风险评分

#### 3. 配置关联
- **churn_warning_churnriskfactor** (无外键，全局配置)
  - 存储各风险因子的权重和阈值配置
  
- **churn_warning_churnwarningconfig** (单例表)
  - 存储系统级别的预警配置

#### 4. 扩展关联
- **churn_warning_churninterventionlog.prediction_id** ← **churn_warning_churnprediction.id** (一对多)
  - 记录针对用户的干预措施和效果

## 🔢 算法详细说明

### 风险评分计算流程

```python
class ChurnPredictionService:
    @classmethod
    def calculate_user_risk_scores(cls, user_id=None):
        """
        风险评分计算主流程
        """
        # 1. 获取用户列表
        users = BigRUser.objects.filter(id=user_id) if user_id else BigRUser.objects.all()
        
        for user in users:
            # 2. 计算各维度风险评分
            login_score = cls._calculate_login_risk(user)      # 登录风险
            recharge_score = cls._calculate_recharge_risk(user)  # 充值风险
            activity_score = cls._calculate_activity_risk(user) # 活跃风险
            
            # 3. 获取或创建预测记录
            prediction, created = ChurnPrediction.objects.get_or_create(user=user)
            
            # 4. 更新各维度评分
            prediction.login_risk_score = login_score
            prediction.recharge_risk_score = recharge_score
            prediction.activity_risk_score = activity_score
            
            # 5. 计算综合风险评分和等级
            prediction.calculate_risk_score()
            
            # 6. 预测流失日期
            prediction.predicted_churn_date = cls._predict_churn_date(user, prediction.risk_score)
            
            # 7. 生成风险因素标签和建议措施
            prediction.risk_factors = cls._generate_risk_factors(user, scores)
            prediction.suggested_actions = cls._generate_suggested_actions(prediction)
            
            # 8. 保存结果
            prediction.save()
```

### 风险等级自动分类

```python
def calculate_risk_score(self):
    """
    综合风险评分计算 - 在 ChurnPrediction 模型中
    """
    # 1. 获取活跃的风险因子配置
    risk_factors = ChurnRiskFactor.objects.filter(is_active=True)
    
    total_score = 0.0
    total_weight = 0.0
    
    # 2. 按权重计算加权平均分
    for factor in risk_factors:
        factor_score = self._get_dimension_score(factor.factor_type)
        total_score += factor_score * factor.weight
        total_weight += factor.weight
    
    # 3. 计算综合评分
    if total_weight > 0:
        self.risk_score = min(100.0, total_score / total_weight)
    else:
        self.risk_score = 0.0
    
    # 4. 自动分类风险等级
    if self.risk_score >= 80:
        self.risk_level = 'critical'      # 严重风险
    elif self.risk_score >= 60:
        self.risk_level = 'high'          # 高风险  
    elif self.risk_score >= 40:
        self.risk_level = 'medium'        # 中等风险
    else:
        self.risk_level = 'low'           # 低风险
    
    return self.risk_score
```

### 流失日期预测算法

```python
def _predict_churn_date(cls, user, risk_score):
    """
    根据风险评分预测流失日期
    """
    if risk_score < 40:
        return None  # 低风险不预测流失时间
    
    # 根据风险评分确定预测天数
    if risk_score >= 80:
        days_to_churn = 7      # 严重风险：7天内流失
    elif risk_score >= 60:
        days_to_churn = 14     # 高风险：14天内流失  
    else:
        days_to_churn = 30     # 中等风险：30天内流失
    
    return timezone.now().date() + timedelta(days=days_to_churn)
```

## ⚙️ 配置参数

### 风险因子配置 (ChurnRiskFactor)

```python
# 默认风险因子配置
RISK_FACTORS = [
    {
        'factor_type': 'login_frequency',    # 登录频率
        'weight': 0.30,                     # 权重30%
        'threshold_value': 10.0,            # 阈值：30天内登录少于10次
        'description': '用户登录频率异常，可能表示活跃度下降'
    },
    {
        'factor_type': 'recharge_frequency', # 充值频率  
        'weight': 0.40,                     # 权重40% (最高)
        'threshold_value': 2.0,             # 阈值：月充值少于2次
        'description': '充值频率下降，可能失去付费意愿'
    },
    {
        'factor_type': 'activity_risk',      # 活跃风险
        'weight': 0.30,                     # 权重30%
        'threshold_value': 50.0,            # 阈值：活跃度下降超过50%
        'description': '在线时长和任务完成度双重下降，用户参与度降低'
    }
]
```

### 系统配置 (ChurnWarningConfig)

```python
# 系统级配置参数
CONFIG = {
    'critical_threshold': 80.0,          # 严重风险阈值
    'high_threshold': 60.0,              # 高风险阈值
    'medium_threshold': 40.0,            # 中等风险阈值
    'prediction_days': 30,               # 预测天数
    'min_confidence_score': 0.7,         # 最低置信度
    'auto_contact_critical': True,       # 自动联系严重风险用户
    'auto_gift_threshold': 85.0,         # 自动赠送礼包阈值
    'stats_retention_days': 90,          # 统计数据保留天数
}
```

## 📊 使用示例

### 1. 计算单个用户风险评分

```python
from churn_warning.services import ChurnPredictionService

# 计算特定用户的风险评分
result = ChurnPredictionService.calculate_user_risk_scores(user_id=12345)
print(f"计算结果: {result['message']}")
```

### 2. 批量更新所有用户风险评分

```python
# 计算所有用户的风险评分
result = ChurnPredictionService.calculate_user_risk_scores()
print(f"更新了 {result['updated_count']} 位用户的风险评分")
```

### 3. 获取不同风险等级的用户

```python
from churn_warning.models import ChurnPrediction

# 获取严重风险用户
critical_users = ChurnPrediction.objects.filter(
    risk_level='critical'
).select_related('user')

# 获取需要在7天内联系的用户  
urgent_users = ChurnPrediction.objects.filter(
    predicted_churn_date__lte=date.today() + timedelta(days=7)
).select_related('user')
```

### 4. 自定义风险因子权重

```python
from churn_warning.models import ChurnRiskFactor

# 调整充值风险权重
recharge_factor = ChurnRiskFactor.objects.get(factor_type='recharge_frequency')
recharge_factor.weight = 0.35  # 提高到35%
recharge_factor.save()

# 重新计算所有用户评分
ChurnPredictionService.calculate_user_risk_scores()
```

## 🔧 维护和优化建议

### 1. 定期重新计算
建议每日凌晨自动执行风险评分计算：

```python
# 在 Django 的 cron job 或 Celery 任务中
from churn_warning.services import ChurnPredictionService, ChurnWarningService

# 重新计算风险评分
ChurnPredictionService.calculate_user_risk_scores()

# 更新统计数据  
ChurnWarningService.update_daily_stats()
```

### 2. 监控和调优
- 定期分析预测准确性，调整权重配置
- 监控各风险等级用户的实际流失率
- 根据实际效果调整阈值参数

### 3. 扩展功能
- 增加更多风险维度（如设备信息、地理位置等）
- 实现机器学习算法优化预测准确性
- 添加A/B测试功能验证干预效果

---

*文档版本: v1.0*  
*最后更新: 2025-08-06*  
*作者: VIPManager 开发团队*