# 流失预警中心 - 快速参考文档

## 🚀 快速开始

### 启动服务
```bash
cd G:\Django\VIPManager
python manage.py runserver
# 访问: http://127.0.0.1:8000/churn-warning/
```

### 初始化测试数据
```bash
python manage.py init_churn_data
# 创建100位用户的流失预测数据和7天统计数据
```

## 📊 风险评判规则速查

| 风险等级 | 评分区间 | 预测流失时间 | 建议措施 |
|---------|---------|-------------|---------|
| 🔴 严重 | 80-100分 | 7天内 | 立即联系+赠送礼包+专属客服 |
| 🟠 高风险 | 60-79分 | 14天内 | 主动联系+充值折扣+活动邀请 |
| 🔵 中风险 | 40-59分 | 30天内 | 赠送礼包+推荐活动 |
| 🟢 低风险 | 0-39分 | 无预测 | 正常维护 |

## 🔢 评分计算公式

```
总风险评分 = 登录风险×30% + 充值风险×40% + 活跃风险×30%
```

### 登录风险计算
```
登录风险 = 登录频率风险×40% + 登录间隔风险×40% + 连续登录风险×20%

登录频率风险 = max(0, (20 - 近30天登录次数) / 20 × 100)
登录间隔风险 = min(100, 距上次登录天数 × 5)  
连续登录风险 = max(0, (10 - 连续登录天数) × 10)
```

### 充值风险计算
```
充值风险 = 充值频率风险×30% + 充值间隔风险×40% + 充值金额风险×30%

充值频率风险 = max(0, (历史月均次数 - 近30天次数) / 历史月均次数 × 100)
充值间隔风险 = min(100, 距上次充值天数 × 3)
充值金额风险 = max(0, (历史月均金额 - 近30天金额) / 历史月均金额 × 100)
```

### 活跃风险计算
```
活跃风险 = 在线时长风险×40% + 活跃度风险×60%

在线时长风险 = max(0, (1 - 近7天平均在线时长 / 历史30天平均在线时长) × 100)
活跃度风险 = max(0, (1 - 近7天平均任务完成度 / 历史30天平均任务完成度) × 100)

特殊情况：连续3天未登录或活跃度为0且在线时长<30分钟连续3天，直接高风险
```

## 🗄️ 核心数据表

| 表名 | 主要字段 | 用途 |
|-----|---------|------|
| `churn_warning_churnprediction` | `user_id`, `risk_score`, `risk_level`, `activity_risk_score` | 用户风险预测主表 |
| `churn_warning_churnriskfactor` | `factor_type`, `weight`, `threshold_value` | 风险因子配置 |
| `churn_warning_churnwarningstats` | `stat_date`, `critical_users`, `high_risk_users` | 每日统计数据 |
| `churn_warning_churninterventionlog` | `prediction_id`, `intervention_type`, `status` | 干预措施记录 |

## 🔧 常用API接口

| 接口 | 方法 | 用途 |
|-----|------|------|
| `/churn-warning/api/stats/` | GET | 获取风险统计数据 |
| `/churn-warning/api/users/` | GET | 获取用户列表 (支持分页/筛选) |
| `/churn-warning/api/batch-contact/` | POST | 批量联系用户 |
| `/churn-warning/api/batch-gift/` | POST | 批量赠送礼包 |
| `/churn-warning/api/users/{id}/` | GET | 获取用户详情 |

## 🎯 核心服务类

### ChurnPredictionService
```python
# 计算所有用户风险评分
ChurnPredictionService.calculate_user_risk_scores()

# 计算单个用户风险评分
ChurnPredictionService.calculate_user_risk_scores(user_id=12345)
```

### ChurnWarningService  
```python
# 获取风险统计
ChurnWarningService.get_risk_stats()

# 获取用户列表
ChurnWarningService.get_users_list(page=1, risk_level='critical')

# 批量联系用户
ChurnWarningService.batch_contact_users([12345, 67890])
```

## ⚙️ 关键配置

### 风险阈值配置
```python
# 在 ChurnWarningConfig 模型中
critical_threshold = 80.0    # 严重风险阈值
high_threshold = 60.0        # 高风险阈值
medium_threshold = 40.0      # 中等风险阈值
```

### 风险因子权重
```python
# 在 ChurnRiskFactor 模型中
login_frequency: 0.30        # 登录频率权重 30%
recharge_frequency: 0.40     # 充值频率权重 40% (最高)
activity_risk: 0.30          # 活跃风险权重 30% (包含在线时长和活跃度)
```

## 🔍 常用查询

### 获取严重风险用户
```python
from churn_warning.models import ChurnPrediction

critical_users = ChurnPrediction.objects.filter(
    risk_level='critical'
).select_related('user').order_by('-risk_score')
```

### 获取需要紧急联系的用户
```python
from datetime import date, timedelta

urgent_users = ChurnPrediction.objects.filter(
    predicted_churn_date__lte=date.today() + timedelta(days=3),
    status='pending'
).select_related('user')
```

### 获取今日风险统计
```python
from churn_warning.models import ChurnWarningStats

today_stats = ChurnWarningStats.objects.filter(
    stat_date=date.today()
).first()
```

## 🛠️ 维护任务

### 每日执行任务
```python
# 1. 重新计算风险评分
python manage.py shell -c "
from churn_warning.services import ChurnPredictionService
ChurnPredictionService.calculate_user_risk_scores()
"

# 2. 更新统计数据  
python manage.py shell -c "
from churn_warning.services import ChurnWarningService
ChurnWarningService.update_daily_stats()
"
```

### 数据清理
```python
# 清理90天前的统计数据
python manage.py shell -c "
from churn_warning.models import ChurnWarningStats
from datetime import date, timedelta
ChurnWarningStats.objects.filter(
    stat_date__lt=date.today() - timedelta(days=90)
).delete()
"
```

## 🎨 前端组件

### 风险等级颜色
```css
.critical { color: #ef4444; }  /* 严重风险 - 红色 */  
.high     { color: #f59e0b; }  /* 高风险 - 橙色 */
.medium   { color: #3b82f6; }  /* 中风险 - 蓝色 */
.low      { color: #10b981; }  /* 低风险 - 绿色 */
```

### 风险因素标签颜色
```css  
.login    { background: #ef4444; }  /* 登录风险 - 红色 */
.recharge { background: #f59e0b; }  /* 充值风险 - 橙色 */
.activity { background: #8b5cf6; }  /* 活跃风险 - 紫色 */
```

## 🚨 故障排除

### 常见问题

1. **风险评分全是0**
   - 检查 ChurnRiskFactor 表是否有有效配置
   - 确认 `is_active=True` 的因子权重总和 > 0

2. **用户列表为空** 
   - 运行 `python manage.py init_churn_data` 初始化数据
   - 检查 BigRUser 表是否有数据

3. **API返回500错误**
   - 检查数据库连接
   - 查看 Django 错误日志
   - 确认所有必要的表都已迁移

### 日志位置
```bash
# Django 日志
tail -f /path/to/django.log

# 数据库查询日志  
# 在 settings.py 中启用 SQL 日志
LOGGING = {
    'loggers': {
        'django.db.backends': {
            'level': 'DEBUG',
        },
    }
}
```

## 📞 技术支持

- **文档位置**: `docs/流失预警风险评判机制详细说明.md`
- **数据库设计**: `docs/churn_warning_database_design.md`  
- **源码位置**: `churn_warning/` 目录
- **测试数据**: 运行 `python manage.py init_churn_data`

---

*版本: v1.0 | 更新: 2025-08-06*