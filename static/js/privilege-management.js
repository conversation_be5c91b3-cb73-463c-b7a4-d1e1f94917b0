// 特权管理页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    const searchInput = document.getElementById('searchInput');
    const privilegesGrid = document.getElementById('privilegesGrid');
    let privilegeCards = privilegesGrid.querySelectorAll('.privilege-card');

    // 初始化页面
    function initializePage() {
        console.log('🚀 初始化特权管理页面...');

        // 加载统计数据
        loadStats();

        // 加载特权数据
        loadPrivileges();

        // 绑定搜索事件
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
        }

        // 绑定工具栏按钮事件
        bindToolbarEvents();

        console.log('✅ 页面初始化完成');
    }

    // 注意：实际初始化在文件末尾的initializeAll()中进行

    // 加载特权列表
    async function loadPrivileges(search = '', page = 1) {
        console.log('🔄 开始加载特权数据...', { search, page });

        const loadingContainer = document.getElementById('loadingContainer');
        const emptyState = document.getElementById('emptyState');
        const privilegesGrid = document.getElementById('privilegesGrid');

        try {
            // 显示加载状态
            showLoading();

            const params = new URLSearchParams({
                search: search,
                page: page,
                page_size: 20
            });

            const url = `/privileges/api/list/?${params}`;
            console.log('📡 请求URL:', url);

            const response = await fetch(url);
            console.log('📡 响应状态:', response.status, response.statusText);

            const data = await response.json();
            console.log('📡 响应数据:', data);

            if (data.success) {
                hideLoading();

                if (data.data.privileges.length === 0) {
                    console.log('📭 没有特权数据，显示空状态');
                    showEmptyState();
                } else {
                    console.log('✅ 特权数据加载成功:', data.data.privileges.length, '个特权');
                    hideEmptyState();
                    renderPrivileges(data.data.privileges);
                }
            } else {
                hideLoading();
                console.error('❌ API返回失败:', data.message);
                showNotification('加载特权数据失败: ' + data.message, 'error');
                showEmptyState();
            }
        } catch (error) {
            hideLoading();
            console.error('❌ 网络请求失败:', error);
            showNotification('网络错误，请稍后重试: ' + error.message, 'error');
            showEmptyState();
        }
    }

    // 显示加载状态
    function showLoading() {
        console.log('⏳ 显示加载状态');
        const loadingContainer = document.getElementById('loadingContainer');
        const emptyState = document.getElementById('emptyState');

        if (loadingContainer) {
            loadingContainer.style.display = 'flex';
            console.log('✅ 加载容器已显示');
        } else {
            console.error('❌ 找不到加载容器元素');
        }

        if (emptyState) emptyState.style.display = 'none';

        // 清空现有卡片
        const existingCards = document.querySelectorAll('.privilege-card');
        existingCards.forEach(card => card.remove());
        console.log('🧹 清空了', existingCards.length, '个现有卡片');
    }

    // 隐藏加载状态
    function hideLoading() {
        const loadingContainer = document.getElementById('loadingContainer');
        if (loadingContainer) loadingContainer.style.display = 'none';
    }

    // 显示空状态
    function showEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'flex';
    }

    // 隐藏空状态
    function hideEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'none';
    }

    // 渲染特权卡片
    function renderPrivileges(privileges) {
        console.log('🎨 开始渲染特权卡片，数量:', privileges.length);
        const privilegesGrid = document.getElementById('privilegesGrid');

        if (!privilegesGrid) {
            console.error('❌ 找不到 privilegesGrid 元素');
            return;
        }

        // 清空现有的特权卡片（保留加载和空状态元素）
        const existingCards = privilegesGrid.querySelectorAll('.privilege-card');
        console.log('🧹 清空现有卡片数量:', existingCards.length);
        existingCards.forEach(card => card.remove());

        // 添加新的特权卡片
        privileges.forEach((privilege, index) => {
            console.log(`🔨 创建第${index + 1}个卡片:`, privilege.name);
            const card = createPrivilegeCard(privilege);
            privilegesGrid.appendChild(card);
            console.log(`✅ 第${index + 1}个卡片已添加到DOM`);
        });

        // 重新获取卡片元素
        privilegeCards = privilegesGrid.querySelectorAll('.privilege-card');
        console.log('📊 DOM中的卡片总数:', privilegeCards.length);

        // 绑定卡片事件
        bindCardEvents();

        console.log(`🎉 渲染完成！共渲染了 ${privileges.length} 个特权卡片`);

        // 确保加载状态被隐藏
        const loadingContainer = document.getElementById('loadingContainer');
        if (loadingContainer) {
            loadingContainer.style.display = 'none';
            console.log('✅ 加载状态已隐藏');
        }
    }

    // 创建特权卡片
    function createPrivilegeCard(privilege) {
        console.log('🔨 创建卡片:', privilege.name, '状态:', privilege.status);

        const card = document.createElement('div');
        card.className = `privilege-card ${privilege.status === 'disabled' ? 'disabled' : ''}`;
        card.setAttribute('data-id', privilege.id);
        card.setAttribute('data-name', privilege.name);
        card.setAttribute('data-type', privilege.category.display_name);
        card.setAttribute('data-description', privilege.description);

        const iconClass = getIconClass(privilege.category.name);
        const statusClass = privilege.status === 'active' ? 'status-active' : 'status-disabled';
        const statusText = privilege.status_display;

        console.log('  图标类:', iconClass, '状态类:', statusClass);

        card.innerHTML = `
            <div class="privilege-header">
                <div class="privilege-info">
                    <div class="privilege-icon ${privilege.category.name}">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="privilege-name">${privilege.name}</div>
                    <div class="privilege-type">${privilege.category.display_name}</div>
                </div>
                <div class="privilege-status ${statusClass}">
                    <div class="status-dot"></div>
                    <span>${statusText}</span>
                </div>
            </div>
            <div class="privilege-description">
                ${privilege.description}
            </div>
            <div class="privilege-footer">
                <div class="privilege-usage">
                    <i class="bi bi-people"></i>
                    <span>使用人数: ${privilege.active_users}</span>
                </div>
                <div class="privilege-actions">
                    <button class="btn btn-sm btn-outline" data-action="edit">
                        <i class="bi bi-pencil"></i>
                        编辑
                    </button>
                    ${privilege.status === 'disabled' ?
                        '<button class="btn btn-sm btn-success" data-action="toggle"><i class="bi bi-play"></i>启用</button>' :
                        '<button class="btn btn-sm btn-primary" data-action="stats"><i class="bi bi-bar-chart"></i>统计</button>'
                    }
                    <button class="btn btn-sm btn-error" data-action="delete">
                        <i class="bi bi-trash"></i>
                        删除
                    </button>
                </div>
            </div>
        `;

        console.log('✅ 卡片HTML已生成，类名:', card.className);
        return card;
    }

    // 获取图标类名
    function getIconClass(categoryName) {
        const iconMap = {
            'item_reward': 'bi-gift',
            'coin_reward': 'bi-percent',
            'attribute_bonus': 'bi-speedometer2',
            'identity_mark': 'bi-star',
            'security_protection': 'bi-shield-check'
        };
        return iconMap[categoryName] || 'bi-award';
    }

    // 搜索处理
    function handleSearch() {
        const searchTerm = searchInput.value.trim();
        loadPrivileges(searchTerm);
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 绑定卡片事件
    function bindCardEvents() {
        privilegeCards.forEach(card => {
            const buttons = card.querySelectorAll('[data-action]');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.getAttribute('data-action');
                    const privilegeId = card.getAttribute('data-id');
                    const privilegeName = card.getAttribute('data-name');

                    // 使用全局的handleCardAction函数
                    if (window.handleCardAction) {
                        window.handleCardAction(action, privilegeId, privilegeName, card);
                    } else {
                        console.error('handleCardAction函数未定义');
                    }
                });
            });
        });
    }


    
    // 切换特权状态
    async function togglePrivilegeStatus(privilegeId, card) {
        try {
            const response = await fetch(`/privileges/api/${privilegeId}/toggle/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                }
            });

            const data = await response.json();

            if (data.success) {
                // 更新卡片状态
                updateCardStatus(card, data.data);

                // 显示成功提示
                showNotification(data.message, 'success');

                // 更新统计数据
                loadStats();
            } else {
                showNotification(data.message, 'error');
            }
        } catch (error) {
            console.error('切换特权状态失败:', error);
            showNotification('操作失败，请稍后重试', 'error');
        }
    }

    // 更新卡片状态
    function updateCardStatus(card, data) {
        const statusElement = card.querySelector('.privilege-status');
        const statusText = statusElement.querySelector('span');
        const actionsContainer = card.querySelector('.privilege-actions');

        if (data.new_status === 'active') {
            // 启用状态
            card.classList.remove('disabled');
            statusElement.className = 'privilege-status status-active';
            statusText.textContent = '已启用';

            // 更新操作按钮
            const toggleBtn = actionsContainer.querySelector('[data-action="toggle"]');
            if (toggleBtn) {
                toggleBtn.innerHTML = '<i class="bi bi-bar-chart"></i>统计';
                toggleBtn.className = 'btn btn-sm btn-primary';
                toggleBtn.setAttribute('data-action', 'stats');
            }
        } else {
            // 禁用状态
            card.classList.add('disabled');
            statusElement.className = 'privilege-status status-disabled';
            statusText.textContent = '已禁用';

            // 更新操作按钮
            const statsBtn = actionsContainer.querySelector('[data-action="stats"]');
            if (statsBtn) {
                statsBtn.innerHTML = '<i class="bi bi-play"></i>启用';
                statsBtn.className = 'btn btn-sm btn-success';
                statsBtn.setAttribute('data-action', 'toggle');
            }
        }

        // 重新绑定事件
        bindCardEvents();
    }
    
    // 加载统计数据
    async function loadStats() {
        try {
            const response = await fetch('/privileges/api/stats/');
            const data = await response.json();

            if (data.success) {
                updateStatsDisplay(data.data.overview);
            } else {
                console.error('加载统计数据失败:', data.message);
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    // 更新统计显示
    function updateStatsDisplay(stats) {
        // 更新统计卡片
        const totalStat = document.getElementById('totalPrivileges');
        const activeStat = document.getElementById('activePrivileges');
        const disabledStat = document.getElementById('disabledPrivileges');
        const usersStat = document.getElementById('totalActiveUsers');

        if (totalStat) totalStat.textContent = stats.total_privileges;
        if (activeStat) activeStat.textContent = stats.active_privileges;
        if (disabledStat) disabledStat.textContent = stats.disabled_privileges;
        if (usersStat) usersStat.textContent = stats.total_active_users.toLocaleString();

        // 更新比率显示
        const activeRate = document.getElementById('activeRate');
        const disabledRate = document.getElementById('disabledRate');

        if (activeRate) {
            activeRate.textContent = `${stats.active_privileges}/${stats.total_privileges} 启用`;
        }
        if (disabledRate) {
            disabledRate.textContent = `${stats.disabled_privileges}/${stats.total_privileges} 禁用`;
        }
    }

    // 获取CSRF Token
    function getCsrfToken() {
        // 首先尝试从cookie获取
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }

        // 如果cookie中没有，尝试从meta标签获取
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) {
            return csrfMeta.getAttribute('content');
        }

        // 如果都没有，尝试从隐藏input获取
        const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (csrfInput) {
            return csrfInput.value;
        }

        console.warn('未找到CSRF token');
        return '';
    }
    
    // 模态框管理
    let currentEditingPrivilegeId = null;
    let formData = null;

    // 初始化模态框
    function initializeModals() {
        const privilegeModal = document.getElementById('privilegeModal');
        const deleteModal = document.getElementById('deleteModal');
        const modalClose = document.getElementById('modalClose');
        const modalCancel = document.getElementById('modalCancel');
        const deleteModalClose = document.getElementById('deleteModalClose');
        const deleteCancel = document.getElementById('deleteCancel');
        const privilegeForm = document.getElementById('privilegeForm');
        const deleteConfirm = document.getElementById('deleteConfirm');

        // 关闭模态框事件
        [modalClose, modalCancel].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', () => hideModal('privilegeModal'));
            }
        });

        [deleteModalClose, deleteCancel].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', () => hideModal('deleteModal'));
            }
        });

        // 点击遮罩关闭模态框
        [privilegeModal, deleteModal].forEach(modal => {
            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        hideModal(modal.id);
                    }
                });
            }
        });

        // 表单提交
        if (privilegeForm) {
            privilegeForm.addEventListener('submit', handleFormSubmit);
        }

        // 删除确认
        if (deleteConfirm) {
            deleteConfirm.addEventListener('click', handleDeleteConfirm);
        }

        // 加载表单数据
        loadFormData();
    }

    // 显示添加特权模态框
    async function showAddModal() {
        console.log('🆕 显示添加特权模态框');
        currentEditingPrivilegeId = null;

        // 重置表单
        resetForm();

        // 设置标题
        document.getElementById('modalTitle').textContent = '添加特权';
        document.getElementById('modalSubmit').querySelector('.btn-text').textContent = '添加';

        // 显示模态框
        showModal('privilegeModal');
    }

    // 显示编辑模态框
    async function showEditModal(privilegeId) {
        console.log('✏️ 显示编辑特权模态框，ID:', privilegeId);
        currentEditingPrivilegeId = privilegeId;

        try {
            // 获取特权详情
            const response = await fetch(`/privileges/api/${privilegeId}/`);
            const data = await response.json();

            if (data.success) {
                // 填充表单
                fillForm(data.data);

                // 设置标题
                document.getElementById('modalTitle').textContent = '编辑特权';
                document.getElementById('modalSubmit').querySelector('.btn-text').textContent = '保存';

                // 显示模态框
                showModal('privilegeModal');
            } else {
                showNotification('获取特权详情失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('获取特权详情失败:', error);
            showNotification('网络错误，请稍后重试', 'error');
        }
    }

    // 显示删除确认模态框
    function showDeleteModal(privilegeId, privilegeName) {
        console.log('🗑️ 显示删除确认模态框，ID:', privilegeId, '名称:', privilegeName);

        // 设置要删除的特权信息
        document.getElementById('deletePrivilegeName').textContent = privilegeName;
        document.getElementById('deleteConfirm').setAttribute('data-privilege-id', privilegeId);

        // 显示模态框
        showModal('deleteModal');
    }



    // 显示统计模态框
    function showStatsModal(privilegeName) {
        showNotification(`查看特权"${privilegeName}"统计功能开发中...`, 'info');
        // TODO: 实现统计图表模态框
    }
    
    // 显示通知
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="bi bi-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="bi bi-x"></i>
            </button>
        `;
        
        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-md);
            padding: 16px;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        `;
        
        // 根据类型设置颜色
        if (type === 'success') {
            notification.style.borderLeftColor = 'var(--success)';
            notification.style.borderLeftWidth = '4px';
        } else if (type === 'warning') {
            notification.style.borderLeftColor = 'var(--warning)';
            notification.style.borderLeftWidth = '4px';
        } else if (type === 'error') {
            notification.style.borderLeftColor = 'var(--error)';
            notification.style.borderLeftWidth = '4px';
        }
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // 关闭按钮事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        });
        
        // 自动关闭
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }
        }, 3000);
    }
    
    // 获取通知图标
    function getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'check-circle';
            case 'warning': return 'exclamation-triangle';
            case 'error': return 'x-circle';
            default: return 'info-circle';
        }
    }
    
    // 模态框辅助函数
    function showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
            setTimeout(() => modal.classList.add('show'), 10);
            document.body.style.overflow = 'hidden';
        }
    }

    function hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        }
    }

    // 加载表单数据
    async function loadFormData() {
        try {
            const response = await fetch('/privileges/api/form-data/');
            const data = await response.json();

            if (data.success) {
                formData = data.data;
                populateFormOptions();
                console.log('✅ 表单数据加载成功');
            } else {
                console.error('❌ 表单数据加载失败:', data.message);
            }
        } catch (error) {
            console.error('❌ 表单数据加载失败:', error);
        }
    }

    // 填充表单选项
    function populateFormOptions() {
        if (!formData) return;

        // 填充分类选项
        const categorySelect = document.getElementById('privilegeCategory');
        if (categorySelect) {
            categorySelect.innerHTML = '<option value="">请选择分类</option>';
            formData.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.display_name;
                categorySelect.appendChild(option);
            });
        }

        // 填充VIP等级选项
        const vipLevelSelect = document.getElementById('privilegeVipLevel');
        if (vipLevelSelect) {
            vipLevelSelect.innerHTML = '<option value="">请选择VIP等级</option>';
            formData.vip_levels.forEach(level => {
                const option = document.createElement('option');
                option.value = level.id;
                option.textContent = `${level.name} (充值≥${level.threshold}元)`;
                vipLevelSelect.appendChild(option);
            });
        }
    }

    // 重置表单
    function resetForm() {
        const form = document.getElementById('privilegeForm');
        if (form) {
            form.reset();
        }
    }

    // 填充表单（编辑时使用）
    function fillForm(privilege) {
        document.getElementById('privilegeName').value = privilege.name || '';
        document.getElementById('privilegeCategory').value = privilege.category?.id || '';
        document.getElementById('privilegeVipLevel').value = privilege.min_vip_level?.id || '';
        document.getElementById('privilegeStatus').value = privilege.status || 'active';
        document.getElementById('privilegeDescription').value = privilege.description || '';
        document.getElementById('privilegeSortOrder').value = privilege.sort_order || 0;
        document.getElementById('privilegeFeatured').checked = privilege.is_featured || false;
    }

    // 绑定工具栏事件
    function bindToolbarEvents() {
        const addBtn = document.querySelector('.toolbar .action-btn.primary');
        const importBtn = document.querySelector('.toolbar .action-btn.secondary');
        const exportBtn = document.querySelector('.toolbar .action-btn.outline');

        console.log('🔗 绑定工具栏事件...');
        console.log('添加按钮:', addBtn);
        console.log('导入按钮:', importBtn);
        console.log('导出按钮:', exportBtn);

        if (addBtn) {
            addBtn.addEventListener('click', showAddModal);
            console.log('✅ 添加按钮事件已绑定');
        } else {
            console.error('❌ 找不到添加按钮');
        }

        if (importBtn) {
            importBtn.addEventListener('click', function() {
                console.log('批量导入特权');
                showNotification('批量导入功能开发中...', 'info');
            });
        }

        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                console.log('导出特权配置');
                showNotification('导出配置功能开发中...', 'info');
            });
        }
    }
    
    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--muted-foreground);
            cursor: pointer;
            padding: 4px;
            border-radius: var(--radius);
            transition: var(--transition-fast);
        }

        .notification-close:hover {
            background-color: var(--muted);
            color: var(--foreground);
        }
    `;
    document.head.appendChild(style);

    // 表单提交处理
    async function handleFormSubmit(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('modalSubmit');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        // 显示加载状态
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
        submitBtn.disabled = true;

        try {
            // 收集表单数据
            const formData = new FormData(e.target);
            const data = {};

            for (let [key, value] of formData.entries()) {
                if (key === 'is_featured') {
                    data[key] = true;
                } else {
                    data[key] = value;
                }
            }

            // 如果没有勾选推荐，设置为false
            if (!data.is_featured) {
                data.is_featured = false;
            }

            console.log('📤 提交数据:', data);

            // 发送请求
            const url = currentEditingPrivilegeId
                ? `/privileges/api/${currentEditingPrivilegeId}/update/`
                : '/privileges/api/create/';

            const method = currentEditingPrivilegeId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                showNotification(result.message, 'success');
                hideModal('privilegeModal');

                // 重新加载数据
                loadPrivileges();
                loadStats();
            } else {
                showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('❌ 表单提交失败:', error);
            showNotification(error.message || '操作失败，请稍后重试', 'error');
        } finally {
            // 恢复按钮状态
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            submitBtn.disabled = false;
        }
    }

    // 删除确认处理
    async function handleDeleteConfirm() {
        const deleteBtn = document.getElementById('deleteConfirm');
        const privilegeId = deleteBtn.getAttribute('data-privilege-id');
        const btnText = deleteBtn.querySelector('.btn-text');
        const btnLoading = deleteBtn.querySelector('.btn-loading');

        if (!privilegeId) {
            showNotification('删除失败：未找到特权ID', 'error');
            return;
        }

        // 显示加载状态
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
        deleteBtn.disabled = true;

        try {
            const response = await fetch(`/privileges/api/${privilegeId}/delete/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCsrfToken()
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification(result.message, 'success');
                hideModal('deleteModal');

                // 重新加载数据
                loadPrivileges();
                loadStats();
            } else {
                showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('❌ 删除失败:', error);
            showNotification('删除失败，请稍后重试', 'error');
        } finally {
            // 恢复按钮状态
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            deleteBtn.disabled = false;
        }
    }

    // 更新卡片操作处理
    function updateCardActionHandling() {
        // 重新定义handleCardAction函数以支持删除
        window.handleCardAction = async function(action, privilegeId, privilegeName, card) {
            switch (action) {
                case 'edit':
                    console.log(`编辑特权: ${privilegeName}`);
                    showEditModal(privilegeId);
                    break;
                case 'stats':
                    console.log(`查看统计: ${privilegeName}`);
                    showStatsModal(privilegeName);
                    break;
                case 'toggle':
                    console.log(`切换特权状态: ${privilegeName}`);
                    await togglePrivilegeStatus(privilegeId, card);
                    break;
                case 'delete':
                    console.log(`删除特权: ${privilegeName}`);
                    showDeleteModal(privilegeId, privilegeName);
                    break;
            }
        };
    }

    // 初始化所有功能
    function initializeAll() {
        initializePage();
        initializeModals();
        updateCardActionHandling();
    }

    // 启动初始化
    initializeAll();

    // 暴露全局函数供模板调用
    window.showAddModal = showAddModal;
    window.privilegeManagement = {
        loadPrivileges,
        loadStats,
        showAddModal,
        showEditModal,
        showDeleteModal,
        showConfigModal,
        showStatsModal,
        showNotification
    };
});
