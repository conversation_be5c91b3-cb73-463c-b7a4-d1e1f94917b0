#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的测试数据生成脚本
"""

import os
import django
import random
from datetime import datetime, timedelta, date
from decimal import Decimal
from django.utils import timezone

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'VIPManager.settings')
django.setup()

from big_r_overview.models import BigRUser
from privileges.models import VIPLevel, PrivilegeCategory, Privilege
from churn_warning.models import ChurnRiskFactor, ChurnWarningConfig

print("开始生成简化测试数据...")

# 1. 生成VIP等级数据
print("1. 生成VIP等级数据...")
vip_levels = [
    {"level": 0, "name": "普通用户", "threshold": Decimal("0"), "description": "未充值用户"},
    {"level": 1, "name": "青铜会员", "threshold": Decimal("100"), "description": "首次充值用户"},
    {"level": 2, "name": "白银会员", "threshold": Decimal("500"), "description": "小额充值用户"},
    {"level": 3, "name": "黄金会员", "threshold": Decimal("1000"), "description": "中等充值用户"},
    {"level": 4, "name": "铂金会员", "threshold": Decimal("2000"), "description": "高额充值用户"},
    {"level": 5, "name": "钻石会员", "threshold": Decimal("5000"), "description": "VIP用户"},
]

for vip_data in vip_levels:
    vip, created = VIPLevel.objects.get_or_create(level=vip_data['level'], defaults=vip_data)
    if created:
        print(f"创建 VIP{vip.level}: {vip.name}")

# 2. 生成特权分类数据
print("2. 生成特权分类数据...")
categories = [
    {
        "name": "item_reward",
        "display_name": "道具奖励",
        "icon": "gift",
        "color": "#f59e0b",
        "description": "游戏内道具和物品奖励",
        "sort_order": 1
    },
    {
        "name": "coin_reward", 
        "display_name": "金币奖励",
        "icon": "coin",
        "color": "#eab308",
        "description": "金币和货币奖励",
        "sort_order": 2
    }
]

for cat_data in categories:
    cat, created = PrivilegeCategory.objects.get_or_create(name=cat_data['name'], defaults=cat_data)
    if created:
        print(f"创建分类: {cat.display_name}")

# 3. 生成特权数据
print("3. 生成特权数据...")
categories_map = {cat.name: cat for cat in PrivilegeCategory.objects.all()}
vip_levels_map = {vip.level: vip for vip in VIPLevel.objects.all()}

privileges_data = [
    {
        "name": "每日登录礼包",
        "category": categories_map["item_reward"],
        "description": "每日登录可获得专属礼包奖励",
        "min_vip_level": vip_levels_map[1],
        "sort_order": 1,
        "is_featured": True
    },
    {
        "name": "每日金币奖励",
        "category": categories_map["coin_reward"],
        "description": "每日可领取固定金币奖励",
        "min_vip_level": vip_levels_map[1],
        "sort_order": 2
    }
]

for priv_data in privileges_data:
    priv, created = Privilege.objects.get_or_create(name=priv_data['name'], defaults=priv_data)
    if created:
        print(f"创建特权: {priv.name}")

# 4. 生成大R用户数据
print("4. 生成大R用户数据...")
user_names = ["龙傲天", "叶凡", "萧炎", "林动", "牧尘", "秦羽", "韩立", "方运", "王林", "孟浩"]
server_names = ["天启", "纵横", "霸业", "传奇"]

for i in range(50):  # 只生成50个用户
    # 随机充值金额
    total_recharge = Decimal(str(random.randint(100, 10000)))
    
    # 生成时间
    created_time = timezone.now() - timedelta(days=random.randint(30, 365))
    first_recharge = created_time + timedelta(days=random.randint(1, 7))
    last_recharge = first_recharge + timedelta(days=random.randint(1, 90))
    last_login = timezone.now() - timedelta(days=random.randint(0, 30))
    
    # 活跃度
    total_login_days = random.randint(20, 300)
    consecutive_login_days = random.randint(0, 30)
    
    # 风险标识  
    is_churn_risk = random.choice([True, False]) if random.random() < 0.3 else False
    churn_levels = ['low', 'medium', 'high', 'critical']
    churn_risk_level = random.choice(churn_levels) if is_churn_risk else 'low'
    
    user_data = {
        'user_id': 20000 + i,
        'username': f"player_{20000+i}",
        'character_name': random.choice(user_names) + str(random.randint(1, 999)),
        'server_id': random.randint(1, 4),
        'server_name': random.choice(server_names) + "服",
        'total_recharge': total_recharge,
        'first_recharge_date': first_recharge,
        'last_recharge_date': last_recharge,
        'last_login_date': last_login,
        'total_login_days': total_login_days,
        'consecutive_login_days': consecutive_login_days,
        'is_potential': random.choice([True, False]) if random.random() < 0.25 else False,
        'is_churn_risk': is_churn_risk,
        'churn_risk_level': churn_risk_level,
        'created_at': created_time
    }
    
    user, created = BigRUser.objects.get_or_create(user_id=user_data['user_id'], defaults=user_data)
    if created:
        print(f"创建用户: {user.character_name}")

# 5. 生成流失风险因子配置
print("5. 生成流失风险因子配置...")
if not ChurnRiskFactor.objects.exists():
    risk_factors = [
        {
            "factor_type": "login_frequency",
            "description": "用户登录频率低于正常水平",
            "weight": 0.25,
            "threshold_value": 3.0
        },
        {
            "factor_type": "recharge_frequency",
            "description": "用户充值频率明显下降", 
            "weight": 0.30,
            "threshold_value": 1.0
        }
    ]
    
    for factor_data in risk_factors:
        ChurnRiskFactor.objects.create(**factor_data)
        print(f"创建风险因子: {factor_data['factor_type']}")

# 6. 生成流失预警配置
print("6. 生成流失预警配置...")
if not ChurnWarningConfig.objects.exists():
    ChurnWarningConfig.objects.create(
        critical_threshold=80.0,
        high_threshold=60.0,
        medium_threshold=40.0,
        prediction_days=30,
        min_confidence_score=0.7,
        auto_contact_critical=True,
        auto_gift_threshold=85.0,
        stats_retention_days=90
    )
    print("创建流失预警配置")

print("简化测试数据生成完成！")
print(f"VIP等级数量: {VIPLevel.objects.count()}")
print(f"特权分类数量: {PrivilegeCategory.objects.count()}")
print(f"特权数量: {Privilege.objects.count()}")
print(f"大R用户数量: {BigRUser.objects.count()}")
print(f"风险因子数量: {ChurnRiskFactor.objects.count()}")
print(f"预警配置数量: {ChurnWarningConfig.objects.count()}")