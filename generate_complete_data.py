#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整的测试数据生成脚本 - 补充所有缺失数据
"""

import os
import django
import random
from datetime import datetime, timedelta, date
from decimal import Decimal
from django.utils import timezone

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'VIPManager.settings')
django.setup()

from big_r_overview.models import *
from churn_warning.models import *
from privileges.models import *
from service_analysis.models import *

print("开始生成完整的测试数据...")

# 获取现有数据
users = list(BigRUser.objects.all())
privileges = list(Privilege.objects.all())
vip_levels = list(VIPLevel.objects.all())
risk_factors = list(ChurnRiskFactor.objects.all())

print(f"当前用户数: {len(users)}")
print(f"当前特权数: {len(privileges)}")

# 1. 生成用户充值记录
print("1. 生成用户充值记录...")
payment_methods = ["微信支付", "支付宝", "银行卡", "Apple Pay", "Google Pay"]

for user in users:
    # 根据用户总充值金额生成充值记录
    total_amount = user.total_recharge
    recharge_count = random.randint(1, 8)  # 每个用户1-8笔充值
    
    remaining_amount = total_amount
    for i in range(recharge_count):
        if i == recharge_count - 1:  # 最后一笔充值
            amount = remaining_amount
        else:
            # 每笔充值金额在总金额的10%-40%之间
            max_single = min(remaining_amount * Decimal("0.4"), remaining_amount - Decimal("50"))
            if max_single >= 50:
                amount = Decimal(str(random.randint(50, int(max_single))))
            else:
                amount = remaining_amount
                
        remaining_amount -= amount
        
        # 生成充值时间（在用户首次和最后充值时间之间）
        time_diff = (user.last_recharge_date - user.first_recharge_date).total_seconds()
        if time_diff > 0:
            random_seconds = random.randint(0, int(time_diff))
            recharge_time = user.first_recharge_date + timedelta(seconds=random_seconds)
        else:
            recharge_time = user.first_recharge_date
        
        UserRechargeRecord.objects.create(
            user=user,
            order_id=f"PAY{user.user_id}{i:03d}{random.randint(1000, 9999)}",
            amount=amount,
            currency="CNY",
            payment_method=random.choice(payment_methods),
            status="success",
            paid_at=recharge_time
        )

print(f"生成充值记录: {UserRechargeRecord.objects.count()} 条")

# 2. 生成用户登录记录
print("2. 生成用户登录记录...")
device_types = ["iOS", "Android", "PC", "Web"]

for user in users:
    # 生成最近30天的登录记录
    login_days = min(user.total_login_days, 30)  # 最多生成30天
    
    # 随机选择哪些天登录
    possible_dates = []
    for i in range(30):
        login_date = (timezone.now() - timedelta(days=i)).date()
        possible_dates.append(login_date)
    
    # 随机选择登录日期
    login_dates = random.sample(possible_dates, min(login_days, len(possible_dates)))
    
    for login_date in login_dates:
        # 生成登录时间
        login_hour = random.randint(8, 23)
        login_minute = random.randint(0, 59)
        login_time = timezone.make_aware(
            datetime.combine(login_date, datetime.min.time()) + 
            timedelta(hours=login_hour, minutes=login_minute)
        )
        
        UserLoginRecord.objects.create(
            user=user,
            login_date=login_date,
            login_time=login_time,
            online_duration=random.randint(30, 480),  # 30分钟到8小时
            ip_address=f"192.168.{random.randint(1,255)}.{random.randint(1,255)}",
            device_type=random.choice(device_types)
        )

print(f"生成登录记录: {UserLoginRecord.objects.count()} 条")

# 3. 生成用户行为分析数据
print("3. 生成用户行为分析数据...")
for user in users:
    # 计算充值相关指标
    recharge_records = UserRechargeRecord.objects.filter(user=user)
    recharge_count = recharge_records.count()
    
    if recharge_count > 0:
        avg_recharge_amount = user.total_recharge / recharge_count
        # 计算充值频率（次/月）
        if user.first_recharge_date and user.last_recharge_date:
            days_span = (user.last_recharge_date - user.first_recharge_date).days
            if days_span > 0:
                recharge_frequency = (recharge_count * 30.0) / days_span  # 转换为次/月
            else:
                recharge_frequency = recharge_count
        else:
            recharge_frequency = 1.0
    else:
        avg_recharge_amount = Decimal("0")
        recharge_frequency = 0.0
    
    # 计算登录相关指标
    login_records = UserLoginRecord.objects.filter(user=user)
    login_count = login_records.count()
    
    if login_count > 0:
        avg_online_duration = sum(record.online_duration for record in login_records) / login_count
        login_frequency = min(login_count * 7.0 / 30, 7.0)  # 每周登录天数，最大7
    else:
        avg_online_duration = 0
        login_frequency = 0.0
    
    # 计算距离上次充值和登录的天数
    days_since_last_recharge = 0
    if user.last_recharge_date:
        days_since_last_recharge = (timezone.now().date() - user.last_recharge_date.date()).days
    
    days_since_last_login = 0
    if user.last_login_date:
        days_since_last_login = (timezone.now().date() - user.last_login_date.date()).days
    
    # 生成潜力评分和流失风险评分
    # 潜力评分基于充值金额、频率等
    potential_score = min(100.0, (
        (float(user.total_recharge) / 100.0) * 0.4 +  # 充值金额影响
        recharge_frequency * 10 * 0.3 +  # 充值频率影响
        login_frequency * 10 * 0.2 +  # 登录频率影响
        (avg_online_duration / 10) * 0.1  # 在线时长影响
    ))
    
    # 流失风险评分基于不活跃程度
    churn_risk_score = min(100.0, (
        days_since_last_login * 2.0 +  # 距离上次登录天数
        days_since_last_recharge * 1.5 +  # 距离上次充值天数
        max(0, (7 - login_frequency) * 10)  # 登录频率不足的惩罚
    ))
    
    UserBehaviorAnalysis.objects.create(
        user=user,
        avg_recharge_amount=avg_recharge_amount,
        recharge_frequency=recharge_frequency,
        days_since_last_recharge=days_since_last_recharge,
        avg_online_duration=int(avg_online_duration),
        login_frequency=login_frequency,
        days_since_last_login=days_since_last_login,
        potential_score=potential_score,
        churn_risk_score=churn_risk_score
    )

print(f"生成行为分析记录: {UserBehaviorAnalysis.objects.count()} 条")

# 4. 生成流失预测数据
print("4. 生成流失预测数据...")
high_risk_users = []  # 收集高风险用户用于后续生成干预记录

for user in users:
    behavior = UserBehaviorAnalysis.objects.get(user=user)
    
    # 基于行为分析计算风险评分
    risk_score = behavior.churn_risk_score
    
    # 根据风险评分确定风险等级
    if risk_score >= 70:
        risk_level = 'critical'
        high_risk_users.append(user)
    elif risk_score >= 50:
        risk_level = 'high'
        high_risk_users.append(user)
    elif risk_score >= 30:
        risk_level = 'medium'
    else:
        risk_level = 'low'
    
    # 更新用户的风险标识
    user.is_churn_risk = risk_score >= 30
    user.churn_risk_level = risk_level
    user.save()
    
    # 生成风险因素
    risk_factors_list = []
    if behavior.days_since_last_login > 7:
        risk_factors_list.append({
            "factor": "长时间未登录",
            "value": behavior.days_since_last_login,
            "weight": 0.3,
            "is_primary": True
        })
    
    if behavior.days_since_last_recharge > 30:
        risk_factors_list.append({
            "factor": "长时间未充值",
            "value": behavior.days_since_last_recharge,
            "weight": 0.4,
            "is_primary": True
        })
    
    if behavior.login_frequency < 3:
        risk_factors_list.append({
            "factor": "登录频率过低",
            "value": behavior.login_frequency,
            "weight": 0.2,
            "is_primary": risk_score >= 50
        })
    
    if behavior.recharge_frequency < 1:
        risk_factors_list.append({
            "factor": "充值频率下降",
            "value": behavior.recharge_frequency,
            "weight": 0.3,
            "is_primary": True
        })
    
    # 生成建议措施
    suggested_actions = []
    if risk_level == 'critical':
        suggested_actions = [
            "立即电话联系用户了解情况",
            "赠送高价值礼包挽留用户",
            "邀请参与限时VIP专属活动",
            "安排专属客服一对一服务"
        ]
    elif risk_level == 'high':
        suggested_actions = [
            "发送关怀短信或邮件",
            "推送个性化优惠活动",
            "客服主动联系了解需求",
            "邀请参与回归活动"
        ]
    elif risk_level == 'medium':
        suggested_actions = [
            "推送游戏更新和新内容通知",
            "邀请参与日常活动",
            "发送个性化推荐内容"
        ]
    else:
        suggested_actions = [
            "保持正常的游戏推送",
            "关注用户行为变化"
        ]
    
    # 预测流失日期
    if risk_score >= 50:
        predicted_days = random.randint(7, 30)  # 高风险用户7-30天
    elif risk_score >= 30:
        predicted_days = random.randint(30, 60)  # 中风险用户30-60天
    else:
        predicted_days = random.randint(60, 120)  # 低风险用户60-120天
    
    predicted_churn_date = timezone.now().date() + timedelta(days=predicted_days)
    
    # 生成置信度
    confidence_score = random.uniform(0.6, 0.95) if risk_score >= 30 else random.uniform(0.3, 0.7)
    
    # 随机处理状态
    if risk_level in ['critical', 'high']:
        status = random.choice(['pending', 'contacted', 'intervened'])
    else:
        status = random.choice(['pending', 'pending', 'contacted'])  # 低风险用户大部分待处理
    
    ChurnPrediction.objects.create(
        user=user,
        risk_level=risk_level,
        risk_score=risk_score,
        login_risk_score=min(100, behavior.days_since_last_login * 3),
        recharge_risk_score=min(100, behavior.days_since_last_recharge * 2),
        activity_risk_score=min(100, max(0, (7 - behavior.login_frequency) * 15)),
        predicted_churn_date=predicted_churn_date,
        confidence_score=confidence_score,
        risk_factors=risk_factors_list,
        suggested_actions=suggested_actions,
        status=status
    )

print(f"生成流失预测记录: {ChurnPrediction.objects.count()} 条")
print(f"高风险用户数: {len(high_risk_users)}")

# 5. 生成流失干预记录
print("5. 生成流失干预记录...")
intervention_types = [
    ('contact', '联系用户'),
    ('gift', '赠送礼包'),
    ('discount', '折扣优惠'),
    ('service', '客服介入'),
    ('event', '活动邀请'),
    ('other', '其他方式')
]

processors = ["客服小王", "客服小李", "客服小张", "客服小陈", "管理员张三", "客服主管"]

# 为已处理的预测记录生成干预记录
processed_predictions = ChurnPrediction.objects.exclude(status='pending')

for prediction in processed_predictions:
    # 每个用户生成1-3个干预记录
    intervention_count = random.randint(1, 3)
    
    for i in range(intervention_count):
        intervention_type, type_name = random.choice(intervention_types)
        
        # 根据风险等级选择不同的干预类型
        if prediction.risk_level == 'critical':
            intervention_type = random.choice(['contact', 'gift', 'service'])
        elif prediction.risk_level == 'high':
            intervention_type = random.choice(['contact', 'discount', 'event'])
        
        # 生成干预描述
        descriptions = {
            'contact': f"主动联系用户{prediction.user.character_name}了解游戏体验和意见反馈",
            'gift': f"为用户{prediction.user.character_name}发放VIP专属礼包，包含稀有道具和金币",
            'discount': f"为用户{prediction.user.character_name}推送限时充值折扣活动",
            'service': f"安排专属客服为用户{prediction.user.character_name}提供一对一服务",
            'event': f"邀请用户{prediction.user.character_name}参与VIP回归专属活动",
            'other': f"为用户{prediction.user.character_name}提供个性化游戏建议和指导"
        }
        
        description = descriptions.get(intervention_type, "其他干预措施")
        
        # 生成执行时间
        execution_date = prediction.created_at + timedelta(days=random.randint(0, 5))
        
        # 生成干预状态和效果
        if prediction.status == 'contacted':
            status = random.choice(['completed', 'executing']) if i == 0 else 'completed'
        elif prediction.status == 'intervened':
            status = 'completed'
        else:
            status = random.choice(['planned', 'executing'])
        
        effectiveness_score = None
        user_response = ""
        completion_date = None
        
        if status == 'completed':
            completion_date = execution_date + timedelta(days=random.randint(0, 2))
            effectiveness_score = random.randint(1, 5)
            
            if effectiveness_score >= 4:
                user_response = "用户反馈良好，表示会继续游戏"
            elif effectiveness_score >= 3:
                user_response = "用户有一定回应，但仍有顾虑"
            else:
                user_response = "用户响应较少，效果一般"
        
        ChurnInterventionLog.objects.create(
            prediction=prediction,
            intervention_type=intervention_type,
            description=description,
            status=status,
            executor=random.choice(processors),
            execution_date=execution_date,
            completion_date=completion_date,
            effectiveness_score=effectiveness_score,
            user_response=user_response,
            notes=f"针对{prediction.get_risk_level_display()}用户的干预措施"
        )

print(f"生成干预记录: {ChurnInterventionLog.objects.count()} 条")

# 6. 生成特权处理记录
print("6. 生成特权处理记录...")
process_details_templates = [
    "用户{character_name}申请{privilege_name}特权，经核实VIP等级符合要求，已批准并发放",
    "系统自动为VIP{vip_level}用户{character_name}发放{privilege_name}奖励",
    "用户{character_name}使用{privilege_name}特权，处理完成",
    "客服为用户{character_name}手动补发{privilege_name}奖励，原因：系统故障导致未到账",
    "用户{character_name}反馈{privilege_name}未生效，经检查后重新发放",
    "VIP用户{character_name}领取{privilege_name}，已确认到账"
]

# 为每个用户生成特权处理记录
for user in users:
    # 根据用户VIP等级生成相应的特权处理记录
    user_vip_level = user.vip_level
    available_privileges = [p for p in privileges if p.min_vip_level.level <= user_vip_level]
    
    if not available_privileges:
        continue
    
    # 每个用户生成2-8个处理记录
    record_count = random.randint(2, 8)
    
    for i in range(record_count):
        privilege = random.choice(available_privileges)
        processor = random.choice(processors)
        
        # 生成处理详情
        template = random.choice(process_details_templates)
        process_detail = template.format(
            character_name=user.character_name,
            privilege_name=privilege.name,
            vip_level=user_vip_level
        )
        
        # 生成处理时间（过去30天内的随机时间）
        processed_at = timezone.now() - timedelta(
            days=random.randint(0, 30),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )
        
        PrivilegeProcessRecord.objects.create(
            character_name=user.character_name,
            character_id=user.user_id,
            vip_level=user_vip_level,
            privilege=privilege,
            process_detail=process_detail,
            processor=processor,
            processed_at=processed_at
        )

print(f"生成特权处理记录: {PrivilegeProcessRecord.objects.count()} 条")

# 7. 生成特权使用记录
print("7. 生成特权使用记录...")
for user in users:
    user_vip_level = user.vip_level
    available_privileges = [p for p in privileges if p.min_vip_level.level <= user_vip_level]
    
    if not available_privileges:
        continue
    
    # 为每个特权生成使用记录
    for privilege in available_privileges:
        # 随机决定这个用户是否使用这个特权
        if random.random() < 0.7:  # 70%概率使用
            # 生成过去30天内的随机使用记录
            usage_days = random.randint(1, 10)  # 1-10天的使用记录
            
            used_dates = set()
            for _ in range(usage_days):
                usage_date = (timezone.now() - timedelta(days=random.randint(0, 30))).date()
                
                if usage_date not in used_dates:
                    used_dates.add(usage_date)
                    usage_count = random.randint(1, 3)  # 每天使用1-3次
                    
                    # 检查是否已存在，避免重复创建
                    if not PrivilegeUsageLog.objects.filter(
                        privilege=privilege,
                        user_id=user.user_id,
                        usage_date=usage_date
                    ).exists():
                        PrivilegeUsageLog.objects.create(
                            privilege=privilege,
                            user_id=user.user_id,
                            usage_date=usage_date,
                            usage_count=usage_count
                        )

print(f"生成特权使用记录: {PrivilegeUsageLog.objects.count()} 条")

# 8. 生成特权统计数据
print("8. 生成特权统计数据...")
for privilege in privileges:
    # 从使用记录计算统计数据
    usage_logs = PrivilegeUsageLog.objects.filter(privilege=privilege)
    
    total_users = usage_logs.values('user_id').distinct().count()
    total_usage = sum(log.usage_count for log in usage_logs)
    
    # 计算不同时间段的活跃用户数
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    active_users_today = usage_logs.filter(usage_date=today).values('user_id').distinct().count()
    active_users_week = usage_logs.filter(usage_date__gte=week_ago).values('user_id').distinct().count()
    active_users_month = usage_logs.filter(usage_date__gte=month_ago).values('user_id').distinct().count()
    
    # 计算人均使用次数
    avg_usage_per_user = total_usage / max(1, total_users)
    
    # 计算受欢迎度评分
    if total_users > 0:
        active_ratio = active_users_week / total_users if total_users > 0 else 0
        popularity_score = min(10.0, active_ratio * avg_usage_per_user * 2)
    else:
        popularity_score = 0.0
    
    PrivilegeStats.objects.create(
        privilege=privilege,
        total_users=total_users,
        active_users_today=active_users_today,
        active_users_week=active_users_week,
        active_users_month=active_users_month,
        total_usage=total_usage,
        avg_usage_per_user=avg_usage_per_user,
        popularity_score=popularity_score
    )

print(f"生成特权统计数据: {PrivilegeStats.objects.count()} 条")

# 9. 生成大R总览统计数据
print("9. 生成大R总览统计数据...")
for i in range(30):  # 生成最近30天的统计数据
    stat_date = timezone.now().date() - timedelta(days=i)
    
    # 计算当日的各种指标
    base_users = len(users)
    daily_variation = random.randint(-2, 3)  # 每日变化
    total_big_r_users = max(0, base_users + daily_variation)
    
    # 统计潜力用户和风险用户
    total_potential_users = len([u for u in users if u.is_potential])
    total_churn_warning_users = len([u for u in users if u.is_churn_risk])
    
    # 计算收入相关指标
    total_revenue = sum(u.total_recharge for u in users)
    avg_arpu = total_revenue / max(1, total_big_r_users)
    
    # VIP分布统计
    vip_distribution = {}
    for level in range(6):  # 0-5级
        vip_distribution[f"vip_{level}"] = len([u for u in users if u.vip_level == level])
    
    # 新增和流失用户（模拟数据）
    new_big_r_users = random.randint(0, 2) if i < 7 else 0  # 最近一周可能有新用户
    lost_big_r_users = random.randint(0, 1)
    
    BigROverviewStats.objects.create(
        stat_date=stat_date,
        total_big_r_users=total_big_r_users,
        total_potential_users=total_potential_users,
        total_churn_warning_users=total_churn_warning_users,
        total_revenue=total_revenue,
        avg_arpu=avg_arpu,
        vip_distribution=vip_distribution,
        new_big_r_users=new_big_r_users,
        lost_big_r_users=lost_big_r_users
    )

print(f"生成大R总览统计: {BigROverviewStats.objects.count()} 条")

# 10. 生成流失预警统计数据
print("10. 生成流失预警统计数据...")
for i in range(30):  # 生成最近30天的统计数据
    stat_date = timezone.now().date() - timedelta(days=i)
    
    # 按风险等级统计用户数量
    predictions = ChurnPrediction.objects.all()
    critical_users = len([p for p in predictions if p.risk_level == 'critical'])
    high_risk_users = len([p for p in predictions if p.risk_level == 'high'])
    medium_risk_users = len([p for p in predictions if p.risk_level == 'medium'])
    low_risk_users = len([p for p in predictions if p.risk_level == 'low'])
    
    # 处理状态统计
    contacted_users = len([p for p in predictions if p.status in ['contacted', 'intervened', 'recovered']])
    intervened_users = len([p for p in predictions if p.status in ['intervened', 'recovered']])
    recovered_users = len([p for p in predictions if p.status == 'recovered'])
    churned_users = len([p for p in predictions if p.status == 'churned'])
    
    # 计算干预成功率
    if intervened_users > 0:
        intervention_success_rate = (recovered_users / intervened_users) * 100
    else:
        intervention_success_rate = 0.0
    
    # 计算平均风险评分
    if predictions:
        avg_risk_score = sum(p.risk_score for p in predictions) / len(predictions)
    else:
        avg_risk_score = 0.0
    
    # 生成变化趋势（相对前一天的变化）
    daily_variation = random.randint(-2, 2)
    
    ChurnWarningStats.objects.create(
        stat_date=stat_date,
        critical_users=max(0, critical_users + random.randint(-1, 1)),
        high_risk_users=max(0, high_risk_users + random.randint(-1, 2)),
        medium_risk_users=max(0, medium_risk_users + random.randint(-2, 2)),
        low_risk_users=max(0, low_risk_users + random.randint(-2, 3)),
        contacted_users=max(0, contacted_users + random.randint(-1, 1)),
        intervened_users=max(0, intervened_users + random.randint(0, 1)),
        recovered_users=max(0, recovered_users + random.randint(0, 1)),
        churned_users=max(0, churned_users + random.randint(0, 1)),
        intervention_success_rate=max(0, min(100, intervention_success_rate + random.randint(-5, 5))),
        avg_risk_score=max(0, min(100, avg_risk_score + random.randint(-3, 3))),
        critical_change=random.randint(-1, 1),
        high_risk_change=random.randint(-2, 2),
        medium_risk_change=random.randint(-2, 2),
        low_risk_change=random.randint(-3, 3)
    )

print(f"生成流失预警统计: {ChurnWarningStats.objects.count()} 条")

print("\n=== 完整数据生成完成！ ===")
print("最终数据统计:")
print(f"BigRUser: {BigRUser.objects.count()}")
print(f"UserRechargeRecord: {UserRechargeRecord.objects.count()}")
print(f"UserLoginRecord: {UserLoginRecord.objects.count()}")
print(f"UserBehaviorAnalysis: {UserBehaviorAnalysis.objects.count()}")
print(f"BigROverviewStats: {BigROverviewStats.objects.count()}")
print()
print(f"ChurnPrediction: {ChurnPrediction.objects.count()}")
print(f"ChurnInterventionLog: {ChurnInterventionLog.objects.count()}")
print(f"ChurnWarningStats: {ChurnWarningStats.objects.count()}")
print()
print(f"PrivilegeProcessRecord: {PrivilegeProcessRecord.objects.count()}")
print(f"PrivilegeUsageLog: {PrivilegeUsageLog.objects.count()}")
print(f"PrivilegeStats: {PrivilegeStats.objects.count()}")

print("\n数据库现在包含完整的测试数据，可以用于系统功能测试！")