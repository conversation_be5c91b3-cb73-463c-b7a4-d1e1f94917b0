# MMO游戏大R用户维护系统 - 数据库表结构说明

## 概述

本文档详细说明了MMO游戏大R用户维护系统中所有数据库表的作用、结构和关系。系统基于Django框架开发，使用SQLite数据库。

---

## 系统表 (Django框架表)

### auth_* 系列表
Django认证系统相关表，负责用户认证、权限管理等功能。

| 表名 | 作用说明 |
|-----|---------|
| `auth_user` | Django用户表，存储系统管理员和操作员账号信息 |
| `auth_group` | 用户组表，定义不同的权限组（如管理员组、客服组等） |
| `auth_permission` | 权限表，定义系统中各种操作权限 |
| `auth_user_groups` | 用户与用户组的多对多关系表 |
| `auth_group_permissions` | 用户组与权限的多对多关系表 |
| `auth_user_user_permissions` | 用户与权限的多对多关系表 |

### django_* 系列表
Django框架核心功能表。

| 表名 | 作用说明 |
|-----|---------|
| `django_migrations` | 数据库迁移记录表，记录所有执行过的数据库迁移 |
| `django_content_type` | 内容类型表，Django内容类型框架的核心表 |
| `django_admin_log` | 管理后台操作日志表，记录管理员在后台的所有操作 |
| `django_session` | 会话表，存储用户会话信息 |

### 其他系统表

| 表名 | 作用说明 |
|-----|---------|
| `sqlite_sequence` | SQLite自动生成的序列表，用于管理自增主键 |

---

## 业务功能表

### 1. 大R用户总览模块 (big_r_overview)

#### `big_r_overview_bigruser` - 大R用户基础信息表
**作用**: 系统的核心表，存储所有大R用户的基础信息和关键指标。

**关键字段**:
- `user_id`: 游戏用户唯一ID
- `username`: 用户名
- `character_name`: 角色名
- `server_id`/`server_name`: 所属服务器信息
- `total_recharge`: 累计充值金额（核心指标）
- `first_recharge_date`/`last_recharge_date`: 首次和最后充值时间
- `last_login_date`: 最后登录时间
- `is_potential`: 是否为潜力用户标识
- `is_churn_risk`: 是否有流失风险标识
- `churn_risk_level`: 流失风险等级

#### `big_r_overview_userrechargerecord` - 用户充值记录表
**作用**: 记录大R用户的每一笔充值交易，用于分析充值行为和计算ARPU值。

**关键字段**:
- `user`: 关联到BigRUser的外键
- `order_id`: 订单号（唯一）
- `amount`: 充值金额
- `payment_method`: 支付方式
- `status`: 支付状态（待支付、成功、失败、已退款）
- `paid_at`: 支付完成时间

#### `big_r_overview_userloginrecord` - 用户登录记录表
**作用**: 记录大R用户的登录行为，用于分析用户活跃度和在线时长。

**关键字段**:
- `user`: 关联到BigRUser的外键
- `login_date`: 登录日期（每天只记录一次）
- `login_time`: 具体登录时间
- `online_duration`: 在线时长（分钟）
- `ip_address`: 登录IP地址
- `device_type`: 设备类型

#### `big_r_overview_bigroverviewstats` - 大R总览统计表
**作用**: 存储每日汇总的大R用户统计数据，用于生成总览页面的图表和趋势分析。

**关键字段**:
- `stat_date`: 统计日期（唯一）
- `total_big_r_users`: 大R用户总数
- `total_potential_users`: 潜力用户数量
- `total_churn_warning_users`: 流失预警用户数
- `total_revenue`: 总收入
- `avg_arpu`: 平均ARPU值
- `vip_distribution`: VIP等级分布（JSON格式）
- `new_big_r_users`/`lost_big_r_users`: 新增和流失用户数

#### `big_r_overview_userbehavioranalysis` - 用户行为分析表
**作用**: 存储基于用户历史数据计算出的行为分析指标，用于潜力挖掘和风险评估。

**关键字段**:
- `user`: 关联到BigRUser的一对一外键
- `avg_recharge_amount`: 平均充值金额
- `recharge_frequency`: 充值频率
- `days_since_last_recharge`: 距离上次充值天数
- `avg_online_duration`: 平均在线时长
- `login_frequency`: 登录频率
- `potential_score`: 潜力评分（0-100）
- `churn_risk_score`: 流失风险评分（0-100）

---

### 2. 流失预警模块 (churn_warning)

#### `churn_warning_churnriskfactor` - 流失风险因子配置表
**作用**: 配置流失风险评估算法中各种风险因子的权重和阈值。

**关键字段**:
- `factor_type`: 风险因子类型（登录频率、充值频率等）
- `description`: 因子描述
- `weight`: 权重（0-1之间）
- `threshold_value`: 阈值
- `is_active`: 是否启用

#### `churn_warning_churnprediction` - 用户流失预测表
**作用**: 存储每个用户的流失风险预测结果和处理状态。

**关键字段**:
- `user`: 关联到BigRUser的一对一外键
- `risk_level`: 风险等级（低、中、高、严重）
- `risk_score`: 综合风险评分（0-100）
- `login_risk_score`/`recharge_risk_score`/`activity_risk_score`: 各维度风险评分
- `predicted_churn_date`: 预测流失日期
- `confidence_score`: 预测置信度
- `risk_factors`: 风险因素列表（JSON格式）
- `suggested_actions`: 建议措施（JSON格式）
- `status`: 处理状态（待处理、已联系、已干预、已挽回、已流失）

#### `churn_warning_churnwarningstats` - 流失预警统计表
**作用**: 存储每日的流失预警统计数据，用于生成预警中心的统计图表。

**关键字段**:
- `stat_date`: 统计日期
- `critical_users`/`high_risk_users`/`medium_risk_users`/`low_risk_users`: 各风险等级用户数
- `contacted_users`/`intervened_users`/`recovered_users`/`churned_users`: 处理状态统计
- `intervention_success_rate`: 干预成功率
- `avg_risk_score`: 平均风险评分
- 各种变化趋势字段

#### `churn_warning_churninterventionlog` - 流失干预记录表
**作用**: 记录针对高风险用户执行的干预措施和效果。

**关键字段**:
- `prediction`: 关联到ChurnPrediction的外键
- `intervention_type`: 干预类型（联系用户、赠送礼包、折扣优惠等）
- `description`: 干预描述
- `status`: 状态（计划中、执行中、已完成、失败）
- `executor`: 执行人
- `execution_date`/`completion_date`: 执行和完成时间
- `effectiveness_score`: 效果评分（1-5）
- `user_response`: 用户反应

#### `churn_warning_churnwarningconfig` - 流失预警配置表
**作用**: 存储流失预警系统的全局配置参数（单例模式）。

**关键字段**:
- `critical_threshold`/`high_threshold`/`medium_threshold`: 各风险等级阈值
- `prediction_days`: 预测天数
- `min_confidence_score`: 最低置信度
- `auto_contact_critical`: 是否自动联系严重风险用户
- `auto_gift_threshold`: 自动赠送礼包阈值

---

### 3. 特权管理模块 (privileges)

#### `privileges_viplevel` - VIP等级表
**作用**: 定义游戏中的VIP等级体系和对应的充值门槛。

**关键字段**:
- `level`: VIP等级（0-10）
- `name`: 等级名称
- `threshold`: 充值门槛
- `description`: 等级描述

#### `privileges_privilegecategory` - 特权分类表
**作用**: 对特权进行分类管理，便于组织和展示。

**关键字段**:
- `name`: 分类名称（唯一标识）
- `display_name`: 显示名称
- `icon`: 图标类名
- `color`: 主题色
- `description`: 分类描述
- `sort_order`: 排序

#### `privileges_privilege` - 特权表
**作用**: 定义具体的VIP特权项目和相关配置。

**关键字段**:
- `name`: 特权名称
- `category`: 关联到PrivilegeCategory的外键
- `description`: 特权描述
- `status`: 状态（已启用、已禁用、维护中）
- `min_vip_level`: 最低VIP等级要求
- `usage_count`/`active_users`: 使用统计
- `sort_order`: 排序
- `is_featured`: 是否推荐特权

#### `privileges_privilegeusagelog` - 特权使用记录表
**作用**: 记录用户使用特权的日志，用于统计分析。

**关键字段**:
- `privilege`: 关联到Privilege的外键
- `user_id`: 用户ID
- `usage_date`: 使用日期
- `usage_count`: 使用次数
- 联合唯一约束：`(privilege, user_id, usage_date)`

#### `privileges_privilegestats` - 特权统计表
**作用**: 存储每个特权的详细使用统计数据。

**关键字段**:
- `privilege`: 关联到Privilege的一对一外键
- `total_users`: 总用户数
- `active_users_today`/`active_users_week`/`active_users_month`: 不同时间段活跃用户数
- `total_usage`: 总使用次数
- `avg_usage_per_user`: 人均使用次数
- `popularity_score`: 受欢迎度评分

---

### 4. 服务分析模块 (service_analysis)

#### `service_analysis_privilegeprocessrecord` - 特权处理记录表
**作用**: 记录客服团队处理用户特权申请的详细记录，用于工单管理和效率分析。

**关键字段**:
- `character_name`/`character_id`: 角色信息
- `vip_level`: 用户VIP等级
- `privilege`: 关联到Privilege的外键
- `process_detail`: 处理详情
- `processor`: 处理人
- `processed_at`: 处理时间

---

## 表关系图

```
BigRUser (核心用户表)
├── UserRechargeRecord (一对多)
├── UserLoginRecord (一对多) 
├── UserBehaviorAnalysis (一对一)
└── ChurnPrediction (一对一)
    └── ChurnInterventionLog (一对多)

VIPLevel (VIP等级)
├── BigRUser.vip_level_info (多对一)
└── Privilege.min_vip_level (一对多)

PrivilegeCategory (特权分类)
└── Privilege (一对多)
    ├── PrivilegeUsageLog (一对多)
    ├── PrivilegeStats (一对一)
    └── PrivilegeProcessRecord (一对多)

ChurnRiskFactor (风险因子配置)
ChurnWarningConfig (预警配置) - 单例
BigROverviewStats (每日统计)
ChurnWarningStats (每日统计)
```

---

## 数据统计信息

截至最后更新：
- **总表数**: 27个（包含系统表和业务表）
- **业务核心表**: 16个
- **Django系统表**: 11个
- **用户数据**: 50个测试用户
- **VIP等级**: 6个等级（0-5级）
- **特权分类**: 2个分类
- **特权项目**: 2个特权

---

## 维护建议

1. **定期备份**: 建议每天备份数据库文件
2. **性能监控**: 关注`BigRUser`和相关记录表的查询性能
3. **数据清理**: 定期清理过期的统计数据和日志记录
4. **索引优化**: 根据查询需求添加必要的数据库索引
5. **数据迁移**: 考虑将来迁移到PostgreSQL以支持更大的数据量

---

*文档生成时间: 2025-08-06*
*系统版本: Django 4.2+ VIP管理系统*