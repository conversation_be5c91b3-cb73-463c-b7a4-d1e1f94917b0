from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from big_r_overview.models import BigRUser


class ChurnRiskFactor(models.Model):
    """流失风险因子配置模型"""
    
    FACTOR_TYPE_CHOICES = [
        ('login_frequency', '登录频率'),
        ('login_duration', '登录时长'),
        ('recharge_frequency', '充值频率'),
        ('recharge_amount', '充值金额'),
        ('activity_risk', '活跃风险'),
        ('online_time_risk', '在线时长风险'),
        ('task_completion_risk', '任务完成度风险'),
    ]
    
    factor_type = models.CharField(
        max_length=50,
        choices=FACTOR_TYPE_CHOICES,
        unique=True,
        verbose_name="风险因子类型"
    )
    description = models.TextField(verbose_name="因子描述")
    weight = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        default=0.1,
        verbose_name="权重(0-1)"
    )
    threshold_value = models.FloatField(
        default=0.0,
        verbose_name="阈值"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "流失风险因子"
        verbose_name_plural = "流失风险因子"
        ordering = ['-weight', 'factor_type']
        
    def __str__(self):
        return f"{self.get_factor_type_display()} (权重: {self.weight})"


class ChurnPrediction(models.Model):
    """用户流失预测模型"""
    
    RISK_LEVEL_CHOICES = [
        ('low', '低风险'),
        ('medium', '中等风险'),
        ('high', '高风险'),
        ('critical', '严重风险'),
    ]
    
    user = models.OneToOneField(
        BigRUser,
        on_delete=models.CASCADE,
        verbose_name="用户"
    )
    risk_level = models.CharField(
        max_length=10,
        choices=RISK_LEVEL_CHOICES,
        verbose_name="风险等级"
    )
    risk_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        verbose_name="风险评分(0-100)"
    )
    
    # 各维度评分详情
    login_risk_score = models.FloatField(default=0.0, verbose_name="登录风险评分")
    recharge_risk_score = models.FloatField(default=0.0, verbose_name="充值风险评分")
    activity_risk_score = models.FloatField(default=0.0, verbose_name="活跃风险评分")
    
    # 预测相关
    predicted_churn_date = models.DateField(
        null=True, 
        blank=True, 
        verbose_name="预测流失日期"
    )
    confidence_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="预测置信度"
    )
    
    # 风险因素标签 (JSON字段存储)
    risk_factors = models.JSONField(default=list, verbose_name="风险因素")
    
    # 建议措施
    suggested_actions = models.JSONField(default=list, verbose_name="建议措施")
    
    # 处理状态
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('contacted', '已联系'),
        ('intervened', '已干预'),
        ('recovered', '已挽回'),
        ('churned', '已流失'),
    ]
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="处理状态"
    )
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "流失预测"
        verbose_name_plural = "流失预测"
        ordering = ['-risk_score', '-updated_at']
        indexes = [
            models.Index(fields=['risk_level']),
            models.Index(fields=['risk_score']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]
        
    def __str__(self):
        return f"{self.user.character_name} - {self.get_risk_level_display()} ({self.risk_score}分)"
    
    @property
    def primary_risk_factors(self):
        """获取主要风险因素"""
        return [factor for factor in self.risk_factors if factor.get('is_primary', False)]
    
    @property 
    def days_until_predicted_churn(self):
        """距离预测流失天数"""
        if not self.predicted_churn_date:
            return None
        
        today = timezone.now().date()
        if self.predicted_churn_date <= today:
            return 0
        
        return (self.predicted_churn_date - today).days
    
    def calculate_risk_score(self):
        """计算综合风险评分"""
        # 获取活跃的风险因子权重
        risk_factors = ChurnRiskFactor.objects.filter(is_active=True)
        
        total_score = 0.0
        total_weight = 0.0
        
        for factor in risk_factors:
            factor_score = 0.0
            
            if factor.factor_type in ['login_frequency', 'login_duration']:
                factor_score = self.login_risk_score
            elif factor.factor_type in ['recharge_frequency', 'recharge_amount']:
                factor_score = self.recharge_risk_score
            elif factor.factor_type in ['activity_risk', 'online_time_risk', 'task_completion_risk']:
                factor_score = self.activity_risk_score
            
            total_score += factor_score * factor.weight
            total_weight += factor.weight
        
        if total_weight > 0:
            self.risk_score = min(100.0, total_score / total_weight)
        else:
            self.risk_score = 0.0
        
        # 根据评分确定风险等级
        if self.risk_score >= 80:
            self.risk_level = 'critical'
        elif self.risk_score >= 60:
            self.risk_level = 'high'
        elif self.risk_score >= 40:
            self.risk_level = 'medium'
        else:
            self.risk_level = 'low'
        
        return self.risk_score


class ChurnWarningStats(models.Model):
    """流失预警统计数据模型"""
    
    # 统计日期
    stat_date = models.DateField(unique=True, verbose_name="统计日期")
    
    # 各风险等级用户数量
    critical_users = models.IntegerField(default=0, verbose_name="严重风险用户数")
    high_risk_users = models.IntegerField(default=0, verbose_name="高风险用户数")
    medium_risk_users = models.IntegerField(default=0, verbose_name="中等风险用户数")
    low_risk_users = models.IntegerField(default=0, verbose_name="低风险用户数")
    
    # 处理统计
    contacted_users = models.IntegerField(default=0, verbose_name="已联系用户数")
    intervened_users = models.IntegerField(default=0, verbose_name="已干预用户数")
    recovered_users = models.IntegerField(default=0, verbose_name="已挽回用户数")
    churned_users = models.IntegerField(default=0, verbose_name="已流失用户数")
    
    # 效果统计
    intervention_success_rate = models.FloatField(default=0.0, verbose_name="干预成功率")
    avg_risk_score = models.FloatField(default=0.0, verbose_name="平均风险评分")
    
    # 变化趋势 (相对于前一天)
    critical_change = models.IntegerField(default=0, verbose_name="严重风险用户变化")
    high_risk_change = models.IntegerField(default=0, verbose_name="高风险用户变化")
    medium_risk_change = models.IntegerField(default=0, verbose_name="中等风险用户变化")
    low_risk_change = models.IntegerField(default=0, verbose_name="低风险用户变化")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "流失预警统计"
        verbose_name_plural = "流失预警统计"
        ordering = ['-stat_date']
        
    def __str__(self):
        return f"{self.stat_date} - 风险用户总数: {self.total_risk_users}"
    
    @property
    def total_risk_users(self):
        """总风险用户数"""
        return self.critical_users + self.high_risk_users + self.medium_risk_users + self.low_risk_users
    
    @property
    def total_processed_users(self):
        """总处理用户数"""
        return self.contacted_users + self.intervened_users + self.recovered_users
    
    def calculate_intervention_success_rate(self):
        """计算干预成功率"""
        if self.intervened_users > 0:
            self.intervention_success_rate = (self.recovered_users / self.intervened_users) * 100
        else:
            self.intervention_success_rate = 0.0
        return self.intervention_success_rate


class ChurnInterventionLog(models.Model):
    """流失干预记录模型"""
    
    INTERVENTION_TYPE_CHOICES = [
        ('contact', '联系用户'),
        ('gift', '赠送礼包'),
        ('discount', '折扣优惠'),
        ('service', '客服介入'),
        ('event', '活动邀请'),
        ('other', '其他方式'),
    ]
    
    STATUS_CHOICES = [
        ('planned', '计划中'),
        ('executing', '执行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]
    
    prediction = models.ForeignKey(
        ChurnPrediction,
        on_delete=models.CASCADE,
        verbose_name="流失预测"
    )
    intervention_type = models.CharField(
        max_length=20,
        choices=INTERVENTION_TYPE_CHOICES,
        verbose_name="干预类型"
    )
    description = models.TextField(verbose_name="干预描述")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='planned',
        verbose_name="状态"
    )
    
    # 执行详情
    executor = models.CharField(max_length=100, blank=True, verbose_name="执行人")
    execution_date = models.DateTimeField(null=True, blank=True, verbose_name="执行时间")
    completion_date = models.DateTimeField(null=True, blank=True, verbose_name="完成时间")
    
    # 效果评估
    effectiveness_score = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="效果评分(1-5)"
    )
    user_response = models.TextField(blank=True, verbose_name="用户反应")
    notes = models.TextField(blank=True, verbose_name="备注")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "干预记录"
        verbose_name_plural = "干预记录"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['prediction', 'intervention_type']),
            models.Index(fields=['status']),
            models.Index(fields=['execution_date']),
        ]
        
    def __str__(self):
        return f"{self.prediction.user.character_name} - {self.get_intervention_type_display()}"
    
    def mark_completed(self, effectiveness_score=None, user_response="", notes=""):
        """标记干预完成"""
        self.status = 'completed'
        self.completion_date = timezone.now()
        
        if effectiveness_score:
            self.effectiveness_score = effectiveness_score
        if user_response:
            self.user_response = user_response
        if notes:
            self.notes = notes
            
        self.save()
        
        # 更新预测状态
        if effectiveness_score and effectiveness_score >= 4:
            self.prediction.status = 'recovered'
        else:
            self.prediction.status = 'intervened'
        self.prediction.save()
        
    def mark_failed(self, reason=""):
        """标记干预失败"""
        self.status = 'failed'
        self.completion_date = timezone.now()
        if reason:
            self.notes = reason
        self.save()


class ChurnWarningConfig(models.Model):
    """流失预警配置模型"""
    
    # 预警阈值配置
    critical_threshold = models.FloatField(default=80.0, verbose_name="严重风险阈值")
    high_threshold = models.FloatField(default=60.0, verbose_name="高风险阈值") 
    medium_threshold = models.FloatField(default=40.0, verbose_name="中等风险阈值")
    
    # 预测配置
    prediction_days = models.IntegerField(default=30, verbose_name="预测天数")
    min_confidence_score = models.FloatField(default=0.7, verbose_name="最低置信度")
    
    # 自动处理配置
    auto_contact_critical = models.BooleanField(default=True, verbose_name="自动联系严重风险用户")
    auto_gift_threshold = models.FloatField(default=85.0, verbose_name="自动赠送礼包阈值")
    
    # 统计配置
    stats_retention_days = models.IntegerField(default=90, verbose_name="统计数据保留天数")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "流失预警配置"
        verbose_name_plural = "流失预警配置"
        
    def __str__(self):
        return f"预警配置 (更新于: {self.updated_at.strftime('%Y-%m-%d %H:%M')})"
    
    @classmethod
    def get_config(cls):
        """获取当前配置（单例模式）"""
        config, created = cls.objects.get_or_create(pk=1)
        return config