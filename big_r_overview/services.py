from django.db.models import Count, Sum, Avg
from django.utils import timezone
from datetime import timedelta, date
from decimal import Decimal
from .models import BigRUser, UserRechargeRecord, UserLoginRecord, BigROverviewStats, UserBehaviorAnalysis


class BigROverviewService:
    """大R用户总览业务逻辑服务"""
    
    @staticmethod
    def calculate_total_big_r_users():
        """计算大R用户总数"""
        # 所有用户都是大R用户，返回总用户数
        return BigRUser.objects.count()
    
    @staticmethod
    def calculate_avg_arpu(period_days=30):
        """计算平均ARPU值"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=period_days)
        
        # 获取期间内的充值总额
        total_revenue = UserRechargeRecord.objects.filter(
            status='success',
            paid_at__date__range=[start_date, end_date]
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        # 获取期间内活跃的用户数
        active_users = BigRUser.objects.filter(
            last_login_date__date__gte=start_date
        ).count()
        
        if active_users > 0:
            return float(total_revenue / active_users)
        return 0
    
    @staticmethod
    def calculate_churn_warning_users():
        """计算流失预警用户数"""
        return BigRUser.objects.filter(
            is_churn_risk=True
        ).count()
    
    @staticmethod
    def calculate_potential_users():
        """计算潜力用户数量"""
        return BigRUser.objects.filter(is_potential=True).count()
    
    @staticmethod
    def calculate_growth_trend(period_days=30, trend_type='cumulative'):
        """计算用户增长趋势

        Args:
            period_days: 时间周期
            trend_type: 趋势类型 ('cumulative': 累计, 'daily': 当日新增)
        """
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=period_days)

        trend_data = []
        for i in range(period_days):
            date_point = start_date + timedelta(days=i)

            if trend_type == 'daily':
                # 计算当日新增用户数
                daily_users = BigRUser.objects.filter(
                    created_at__date=date_point
                ).count()
                count = daily_users
            else:
                # 计算截止到该日期的累计用户数
                cumulative_users = BigRUser.objects.filter(
                    created_at__date__lte=date_point
                ).count()
                count = cumulative_users

            trend_data.append({
                'date': date_point.strftime('%m月%d日'),
                'count': count
            })

        return trend_data
    
    @staticmethod
    def calculate_vip_distribution():
        """计算VIP等级分布"""
        # 使用BigRUser模型的vip_level属性来计算分布
        from collections import defaultdict
        
        # 统计每个VIP等级的用户数量
        distribution_counts = defaultdict(int)
        
        for user in BigRUser.objects.all():
            vip_level = user.vip_level  # 使用模型的vip_level属性
            distribution_counts[vip_level] += 1
        
        total_users = sum(distribution_counts.values())
        
        # 获取VIP等级名称映射
        try:
            from privileges.models import VIPLevel
            vip_level_names = {}
            for vip in VIPLevel.objects.all():
                vip_level_names[vip.level] = vip.name
        except Exception:
            vip_level_names = {}
        
        result = []
        # 按VIP等级从高到低排序
        for vip_level in sorted(distribution_counts.keys(), reverse=True):
            count = distribution_counts[vip_level]
            percentage = (count / total_users * 100) if total_users > 0 else 0
            
            # 使用数据库中的VIP等级名称，如果没有则使用传统格式
            level_name = vip_level_names.get(vip_level, f"VIP{vip_level}")
            
            result.append({
                'level': level_name,
                'level_number': vip_level,
                'count': count,
                'percentage': round(percentage, 1)
            })
        
        return result
    
    @staticmethod
    def get_metrics_data():
        """获取核心指标数据"""
        total_users = BigROverviewService.calculate_total_big_r_users()
        avg_arpu = BigROverviewService.calculate_avg_arpu()
        churn_warning = BigROverviewService.calculate_churn_warning_users()
        potential_users = BigROverviewService.calculate_potential_users()
        
        # 计算变化趋势（与上月对比）
        last_month_stats = BigROverviewStats.objects.filter(
            stat_date=timezone.now().date() - timedelta(days=30)
        ).first()
        
        if last_month_stats:
            total_change = ((total_users - last_month_stats.total_big_r_users) / 
                          last_month_stats.total_big_r_users * 100) if last_month_stats.total_big_r_users > 0 else 0
            arpu_change = ((avg_arpu - float(last_month_stats.avg_arpu)) / 
                          float(last_month_stats.avg_arpu) * 100) if last_month_stats.avg_arpu > 0 else 0
            churn_change = ((churn_warning - last_month_stats.total_churn_warning_users) / 
                           last_month_stats.total_churn_warning_users * 100) if last_month_stats.total_churn_warning_users > 0 else 0
            potential_change = ((potential_users - last_month_stats.total_potential_users) / 
                               last_month_stats.total_potential_users * 100) if last_month_stats.total_potential_users > 0 else 0
        else:
            total_change = arpu_change = churn_change = potential_change = 0
        
        return {
            'total_big_r_users': {
                'value': total_users,
                'change': f"{total_change:+.1f}%",
                'change_type': 'positive' if total_change >= 0 else 'negative'
            },
            'avg_arpu': {
                'value': avg_arpu,
                'formatted': f"¥{avg_arpu:,.0f}",
                'change': f"{arpu_change:+.1f}%",
                'change_type': 'positive' if arpu_change >= 0 else 'negative'
            },
            'churn_warning_users': {
                'value': churn_warning,
                'change': f"{churn_change:+.1f}%",
                'change_type': 'positive' if churn_change <= 0 else 'negative'  # 流失减少是好事
            },
            'potential_users': {
                'value': potential_users,
                'change': f"{potential_change:+.1f}%",
                'change_type': 'positive' if potential_change >= 0 else 'negative'
            },
            'last_updated': timezone.now().isoformat()
        }


class UserClassificationService:
    """用户分类服务"""
    
    @staticmethod
    def identify_potential_users():
        """识别潜力用户"""
        for user in BigRUser.objects.all():
            score = UserClassificationService.calculate_potential_score(user)
            
            # 更新用户行为分析
            analysis, created = UserBehaviorAnalysis.objects.get_or_create(user=user)
            analysis.potential_score = score
            analysis.save()
            
            # 判定为潜力用户
            if score >= 70 and user.total_recharge >= 100:
                user.is_potential = True
                user.save()
    
    @staticmethod
    def calculate_potential_score(user):
        """计算用户潜力评分"""
        score = 0
        
        # 充值金额权重 (40%)
        if user.total_recharge >= 500:
            score += 40
        elif user.total_recharge >= 200:
            score += 30
        elif user.total_recharge >= 100:
            score += 20
        elif user.total_recharge >= 50:
            score += 10
        
        # 活跃度权重 (30%)
        if user.last_login_date:
            days_since_login = (timezone.now().date() - user.last_login_date.date()).days
            if days_since_login <= 1:
                score += 30
            elif days_since_login <= 3:
                score += 25
            elif days_since_login <= 7:
                score += 20
            elif days_since_login <= 14:
                score += 10
        
        # VIP等级权重 (20%)
        score += min(user.vip_level * 4, 20)
        
        # 充值频率权重 (10%)
        analysis = getattr(user, 'userbehavioranalysis', None)
        if analysis and analysis.recharge_frequency >= 2:
            score += 10
        elif analysis and analysis.recharge_frequency >= 1:
            score += 5
        
        return min(score, 100)
    
    @staticmethod
    def identify_churn_risk_users():
        """识别流失风险用户"""
        for user in BigRUser.objects.all():
            risk_score = UserClassificationService.calculate_churn_risk_score(user)
            
            # 更新用户行为分析
            analysis, created = UserBehaviorAnalysis.objects.get_or_create(user=user)
            analysis.churn_risk_score = risk_score
            analysis.save()
            
            # 判定流失风险等级
            if risk_score >= 80:
                user.churn_risk_level = 'critical'
                user.is_churn_risk = True
            elif risk_score >= 60:
                user.churn_risk_level = 'high'
                user.is_churn_risk = True
            elif risk_score >= 40:
                user.churn_risk_level = 'medium'
                user.is_churn_risk = False
            else:
                user.churn_risk_level = 'low'
                user.is_churn_risk = False
            
            user.save()
    
    @staticmethod
    def calculate_churn_risk_score(user):
        """计算流失风险评分"""
        score = 0
        
        # 登录间隔权重 (40%)
        if user.last_login_date:
            days_since_login = (timezone.now().date() - user.last_login_date.date()).days
            if days_since_login >= 14:
                score += 40
            elif days_since_login >= 7:
                score += 30
            elif days_since_login >= 3:
                score += 20
            elif days_since_login >= 1:
                score += 10
        else:
            score += 40
        
        # 充值间隔权重 (35%)
        if user.last_recharge_date:
            days_since_recharge = (timezone.now().date() - user.last_recharge_date.date()).days
            if days_since_recharge >= 60:
                score += 35
            elif days_since_recharge >= 30:
                score += 25
            elif days_since_recharge >= 14:
                score += 15
            elif days_since_recharge >= 7:
                score += 5
        else:
            score += 35
        
        # 活跃度下降权重 (25%)
        analysis = getattr(user, 'userbehavioranalysis', None)
        if analysis:
            if analysis.login_frequency < 2:  # 每周登录少于2天
                score += 25
            elif analysis.login_frequency < 4:
                score += 15
            elif analysis.login_frequency < 6:
                score += 5
        
        return min(score, 100)
    
    @staticmethod
    def update_user_data():
        """更新用户相关数据"""
        UserClassificationService.identify_potential_users()
        UserClassificationService.identify_churn_risk_users()


class StatsGenerationService:
    """统计数据生成服务"""
    
    @staticmethod
    def generate_daily_stats(target_date=None):
        """生成每日统计数据"""
        if target_date is None:
            target_date = timezone.now().date()
        
        # 获取核心指标
        total_users = BigROverviewService.calculate_total_big_r_users()
        potential_users = BigROverviewService.calculate_potential_users()
        churn_warning_users = BigROverviewService.calculate_churn_warning_users()
        avg_arpu = BigROverviewService.calculate_avg_arpu()
        vip_distribution = BigROverviewService.calculate_vip_distribution()
        
        # 计算总收入
        total_revenue = UserRechargeRecord.objects.filter(
            status='success',
            paid_at__date=target_date
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        # 创建或更新统计记录
        stats, created = BigROverviewStats.objects.get_or_create(
            stat_date=target_date,
            defaults={
                'total_big_r_users': total_users,
                'total_potential_users': potential_users,
                'total_churn_warning_users': churn_warning_users,
                'total_revenue': total_revenue,
                'avg_arpu': Decimal(str(avg_arpu)),
                'vip_distribution': vip_distribution,
            }
        )
        
        if not created:
            # 更新现有记录
            stats.total_big_r_users = total_users
            stats.total_potential_users = potential_users
            stats.total_churn_warning_users = churn_warning_users
            stats.total_revenue = total_revenue
            stats.avg_arpu = Decimal(str(avg_arpu))
            stats.vip_distribution = vip_distribution
            stats.save()
        
        return stats
